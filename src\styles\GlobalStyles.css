/* Global styles to hide scrollbars across the entire application */

/* Hide scrollbars for all elements */
* {
  scrollbar-width: none !important; /* Firefox */
  -ms-overflow-style: none !important; /* IE and Edge */
}

/* Hide WebKit scrollbars */
*::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
}

/* Ensure content is still scrollable but scrollbars are hidden */
html, body, #root {
  margin: 0;
  padding: 0;
  overflow-x: hidden !important;
  -webkit-overflow-scrolling: touch;
}

/* Allow vertical scrolling for content areas but hide the scrollbar */
.content-area {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Specific fix for the PageLayout content area in App.js */
.page-content {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* Ensure Conversation component doesn't show scrollbars */
.conversation-container {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}

/* Ensure Minutes component doesn't show scrollbars */
.minutes-container {
  scrollbar-width: none !important;
  -ms-overflow-style: none !important;
}
