import os
import sys
import time
from datetime import timedelta

# Add the Pipeline Phase2 directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase2'))

# Import both versions for comparison
print("Loading original Phase2...")
from PHASE2 import process_meeting_transcript_enhanced as original_process

print("Loading optimized Phase2V2...")
from Phase2V2 import process_meeting_transcript_enhanced as optimized_process

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def test_performance(transcript_path, use_optimized=True):
    """Test processing performance with timing."""
    # Load transcript
    with open(transcript_path, 'r', encoding='utf-8') as file:
        transcript = file.read()
    
    # Truncate if needed for testing
    if len(transcript) > 50000:
        print(f"Truncating transcript from {len(transcript)} to 50000 characters for testing")
        transcript = transcript[:50000]
    
    # Start timing
    start_time = time.time()
    
    # Process with appropriate function
    if use_optimized:
        print("Using optimized Phase2V2 for processing...")
        minutes = optimized_process(
            transcript,
            include_executive_summary=True,
            enhance_transcript=False  # Set to False to avoid potential issues
        )
    else:
        print("Using original Phase2 for processing...")
        minutes = original_process(
            transcript,
            include_executive_summary=True
        )
    
    # Calculate processing time
    processing_time = time.time() - start_time
    
    # Print results
    print("\nProcessing Results:")
    print("-----------------")
    print(f"Processing completed in {format_time(processing_time)} ({processing_time:.2f} seconds)")
    print(f"Output length: {len(minutes)} characters")
    print(f"Words generated: {len(minutes.split())}")
    print(f"Processing speed: {len(transcript) / processing_time:.2f} characters per second")
    
    return processing_time, len(minutes.split())

def main():
    """Main function to run performance tests."""
    # Check if transcript path is provided
    if len(sys.argv) < 2:
        print("Usage: python test_performance.py <transcript_path> [--original]")
        print("Example: python test_performance.py sample_transcript.txt")
        return
    
    transcript_path = sys.argv[1]
    use_optimized = "--original" not in sys.argv
    
    # Verify file exists
    if not os.path.exists(transcript_path):
        print(f"Error: File not found: {transcript_path}")
        return
    
    # Run performance test
    test_performance(transcript_path, use_optimized)

if __name__ == "__main__":
    main()
