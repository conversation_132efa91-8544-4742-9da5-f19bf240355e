import React, { useState, useEffect, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Avatar from '../assets/user.png';
import bigLogo from '../assets/logo.png';
import homeIcon from '../assets/home.png';
import conversationIcon from '../assets/conversation.png';
import minutesIcon from '../assets/meeting_minutes.png';
import signOutIcon from '../assets/sign_out.png';
import settingsIcon from '../assets/settings.png';
import { getUsername } from '../services/authService';
import '../styles/SidebarStyles.css';

const Sidebar = () => {
  const location = useLocation();
  const [activeMenu, setActiveMenu] = useState(location.pathname);
  // Directly compute if we're on the settings page
  const isSettingsPage = location.pathname === '/settings';
  const [userData, setUserData] = useState({
    username: '',
    email: ''
  });
  const [profilePicture, setProfilePicture] = useState('');

  // Function to load user data from localStorage
  const loadUserData = useCallback(() => {
    console.log('Loading user data in sidebar');
    // Get user data from localStorage
    let username = localStorage.getItem('username') || 'User';
    let email = localStorage.getItem('email');
    let profilePic = localStorage.getItem('profilePicture');

    // Capitalize the first letter of the username for better display
    if (username) {
      username = username.charAt(0).toUpperCase() + username.slice(1);
    }

    // If email is not available, create a default one
    if (!email) {
      const usernameForEmail = localStorage.getItem('username') || 'user';
      email = usernameForEmail.toLowerCase() + '@lspu.edu.ph';
    }

    setUserData({
      username,
      email
    });

    if (profilePic) {
      console.log('Setting profile picture in sidebar');
      setProfilePicture(profilePic);
    }
  }, []);

  // Update active menu when location changes
  useEffect(() => {
    setActiveMenu(location.pathname);
  }, [location]);

  // Load user data on component mount
  useEffect(() => {
    console.log('Sidebar component mounted, loading user data');
    loadUserData();
  }, [loadUserData]);

  // Listen for profile updates
  useEffect(() => {
    const handleProfileUpdate = (event) => {
      console.log('Profile update event received in sidebar', event.detail);
      if (event.detail && event.detail.profilePicture) {
        console.log('Setting profile picture from event:', event.detail.profilePicture.substring(0, 50) + '...');
        setProfilePicture(event.detail.profilePicture);
      } else {
        // If no specific profile picture in the event, reload from localStorage
        console.log('No profile picture in event, reloading from localStorage');
        loadUserData();
      }
    };

    // Add event listener for profile updates
    window.addEventListener('profileUpdated', handleProfileUpdate);

    // Check if there's a profile picture in localStorage on mount
    const storedProfilePic = localStorage.getItem('profilePicture');
    if (storedProfilePic) {
      console.log('Found profile picture in localStorage, setting it');
      setProfilePicture(storedProfilePic);
    }

    // Clean up the event listener when component unmounts
    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdate);
    };
  }, [loadUserData]);

  const menuItems = [
    { name: 'Home', path: '/home', icon: homeIcon },
    // Removed Transcriptions menu item as it's redundant with All Transcripts
    { name: 'Transcripts', path: '/all-transcripts', icon: conversationIcon },
    { name: 'Minutes of the Meeting', path: '/minutes', icon: minutesIcon }
  ];

  return (
    <div className="sidebar">
      {/* Big Logo Section */}
      <div className="logo-section">
        <img src={bigLogo} alt="Logo" className="big-logo" />
      </div>

      {/* User Avatar and Info */}
      <div className="user-info">
        <img
          src={profilePicture || Avatar}
          alt="User Avatar"
          className="avatar"
          style={{
            objectFit: 'cover',
            width: '50px',
            height: '50px'
          }}
        />
        <div className="user-details">
          <p className="user-name">{userData.username}</p>
          <p className="user-email">{userData.email}</p>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="nav-menu">
        <ul>
          {menuItems.map(item => (
            <li key={item.name}>
              <Link
                to={item.path}
                className={`menu-item ${activeMenu === item.path ? 'active' : ''}`}
                onClick={() => setActiveMenu(item.path)}
              >
                <img
                  src={item.icon}
                  alt={item.name}
                  className="menu-icon"
                  style={{
                    filter: activeMenu === item.path ? 'brightness(0) invert(1)' : 'none'
                  }}
                />
                {item.name}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer Section */}
      <div className="sidebar-footer">
        <Link
          to="/settings"
          className={`footer-item ${isSettingsPage ? 'active' : ''}`}
        >
          <img
            src={settingsIcon}
            alt="Settings"
            className="menu-icon"
            style={{
              filter: isSettingsPage ? 'brightness(0) invert(1)' : 'none'
            }}
          />
          Settings
        </Link>
        <Link to="/" className="footer-item">
          <img src={signOutIcon} alt="Sign Out" className="menu-icon" />
          Sign Out
        </Link>
      </div>
    </div>
  );
};

export default Sidebar;
