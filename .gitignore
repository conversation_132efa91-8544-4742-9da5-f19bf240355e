# Dependencies
node_modules/
/.pnp
.pnp.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
/coverage
tests/fixtures/output/
tests/temp/

# Production
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# PyTorch/TensorFlow models (large files)
*.pth
*.pt
*.h5
*.keras
*.safetensors
*.bin

# Exclude all large model files everywhere
**/model.safetensors
**/pytorch_model.bin
**/model.bin

# Keep essential config files for BART model
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/config.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/tokenizer.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/vocab.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/merges.txt
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/special_tokens_map.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/tokenizer_config.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/generation_config.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/README.md

# Large dataset files (>100MB)
infrastructure/storage/DATA/
infrastructure/storage/uploads/*.mp4
infrastructure/storage/uploads/*.avi
infrastructure/storage/uploads/*.mov
infrastructure/storage/uploads/*.wav
infrastructure/storage/uploads/*.mp3

# Logs
infrastructure/logs/
*.log

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Large binary files
*.pyd
*.lib
*.dll

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Cache directories
.cache/
.npm/
.yarn/

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.inc
