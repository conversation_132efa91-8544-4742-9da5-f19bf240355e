# Dependencies
node_modules/
/.pnp
.pnp.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
/coverage
tests/fixtures/output/
tests/temp/

# Production
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# PyTorch/TensorFlow models (large files)
*.pth
*.pt
*.h5
*.keras
# Exclude large BART model files but keep directory structure
core/pipelines/Pipeline Phase2/meeting-summary-bart-large/model.safetensors
core/pipelines/Pipeline Phase2/meeting-summary-bart-large/pytorch_model.bin
core/pipelines/Pipeline Phase2/meeting-summary-bart-large/*.bin
# Keep config files but exclude large model weights
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/config.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/tokenizer.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/vocab.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/merges.txt
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/special_tokens_map.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/tokenizer_config.json
!core/pipelines/Pipeline Phase2/meeting-summary-bart-large/generation_config.json

# Large dataset files (>100MB)
infrastructure/storage/DATA/
infrastructure/storage/uploads/*.mp4
infrastructure/storage/uploads/*.avi
infrastructure/storage/uploads/*.mov
infrastructure/storage/uploads/*.wav
infrastructure/storage/uploads/*.mp3

# Logs
infrastructure/logs/
*.log

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Large binary files
*.pyd
*.lib
*.dll

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# Cache directories
.cache/
.npm/
.yarn/

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.inc
