# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*.so
.Python
env/
venv/
.env/

# Large binary files
*.pyd
*.lib
*.dll

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.idea/
.vscode/

# Upload directories
uploads/
server/uploads/
*.wav

# Keep transcript directory but ignore contents
transcript-whisper/*
!transcript-whisper/.gitkeep
