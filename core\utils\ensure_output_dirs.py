"""
Ensure output directories exist and have proper permissions.
This script creates the necessary directories for transcript and minutes output.
"""

import os
import sys

def ensure_output_dirs():
    """
    Create output directories if they don't exist.
    Returns a dictionary with paths to the created directories.
    """
    # Get the base directory (project root)
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # Define the directories to create
    dirs = {
        'transcription': os.path.join(base_dir, 'python', 'transcription'),
        'minutes': os.path.join(base_dir, 'python', 'minutes of the meeting'),
        'uploads': os.path.join(base_dir, 'server', 'uploads'),
        'processed': os.path.join(base_dir, 'server', 'uploads', 'processed')
    }
    
    # Create each directory if it doesn't exist
    for name, path in dirs.items():
        try:
            if not os.path.exists(path):
                os.makedirs(path)
                print(f"Created directory: {path}")
            else:
                print(f"Directory already exists: {path}")
                
            # Ensure the directory is writable
            if not os.access(path, os.W_OK):
                print(f"Warning: Directory {path} is not writable")
        except Exception as e:
            print(f"Error creating directory {path}: {e}")
    
    return dirs

if __name__ == "__main__":
    print("Ensuring output directories exist...")
    dirs = ensure_output_dirs()
    print("Done.")
    
    # Print the directories
    print("\nOutput directories:")
    for name, path in dirs.items():
        print(f"  {name}: {path}")
