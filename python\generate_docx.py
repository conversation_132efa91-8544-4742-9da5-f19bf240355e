#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Generate a DOCX file from a template using python-docx
"""

import sys
import os
import json
import traceback
from docx import Document
from docx.shared import Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH

def main():
    """
    Main function to generate a DOCX file from a template
    """
    try:
        # Check arguments
        if len(sys.argv) != 4:
            print("Usage: python generate_docx.py <template_path> <data_path> <output_path>")
            sys.exit(1)

        template_path = sys.argv[1]
        data_path = sys.argv[2]
        output_path = sys.argv[3]

        # Check if template file exists
        if not os.path.exists(template_path):
            print(f"Template file not found: {template_path}")
            sys.exit(1)

        # Check if data file exists
        if not os.path.exists(data_path):
            print(f"Data file not found: {data_path}")
            sys.exit(1)

        # Load data from JSON file
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract data
        title = data.get('title', 'Minutes of the Meeting')
        date_time = data.get('dateTime', '')
        meeting_date = data.get('meetingDate', '')
        attendees = data.get('attendees', [])
        content = data.get('content', '')
        action_items = data.get('actionItems', [])
        opening_statement = data.get('openingStatement', '')
        facilitator = data.get('facilitator', '')
        meeting_time = data.get('meetingTime', '')
        meeting_platform = data.get('meetingPlatform', '')

        # If no opening statement is provided but we have other meeting details, generate one
        if not opening_statement and (meeting_date or meeting_time or facilitator or meeting_platform):
            platform = meeting_platform or "online"
            date = meeting_date or ""
            time = meeting_time or ""
            facilitator_text = facilitator or "the meeting facilitator"
            attendees_text = "with the participation of " + ", ".join(attendees) if attendees else "with all attendees present"

            # Build the opening statement
            opening_parts = []
            opening_parts.append(f"The {title}")

            if platform:
                opening_parts.append(f"held {platform}")

            if date:
                opening_parts.append(f"on {date}")

            if time:
                opening_parts.append(f"started at {time}")

            opening_statement = ", ".join(opening_parts) + "."

            if facilitator_text:
                opening_statement += f" It was facilitated by {facilitator_text}, {attendees_text}."

        # We'll handle the opening statement separately in the document generation
        # so we don't need to prepend it to the content here

        # Additional processing to fix any remaining question marks in the content
        # This is a backup in case the client-side filtering missed anything
        if content:
            import re

            # Replace question marks followed by capital letters with periods
            content = re.sub(r'\? ([A-Z])', r'. \1', content)

            # Remove names in square brackets
            content = re.sub(r'\[[^\]]+\]', '', content)

            # Filter out any remaining Key Participants section
            # Split the content by lines
            lines = content.split('\n')
            filtered_lines = []
            skip_line = False

            for i, line in enumerate(lines):
                # If we find a line with "Key Participants", start skipping
                if "Key Participants" in line:
                    skip_line = True
                    continue

                # If we're skipping and find a new section (starts with ##) or a blank line followed by a non-blank line
                # that doesn't start with a list item, stop skipping
                if skip_line and (line.startswith('##') or
                                 (line.strip() == '' and i + 1 < len(lines) and
                                  lines[i + 1].strip() != '' and not lines[i + 1].strip().startswith('-'))):
                    skip_line = False

                # Add the line if we're not skipping
                if not skip_line:
                    filtered_lines.append(line)

            # Join the filtered lines back into content
            content = '\n'.join(filtered_lines)

        # Load the template document
        doc = Document(template_path)

        # Replace the hardcoded date in the template with the meeting date
        if meeting_date:
            print(f"Replacing date with: {meeting_date}")

            # Function to replace text in a paragraph
            def replace_date_in_paragraph(paragraph):
                print(f"Checking paragraph: '{paragraph.text}'")

                # Check if paragraph contains "HELD"
                if "HELD" in paragraph.text:
                    print(f"Found 'HELD' in paragraph: '{paragraph.text}'")

                    # Print all runs in this paragraph for debugging
                    print(f"Paragraph has {len(paragraph.runs)} runs:")
                    for i, run in enumerate(paragraph.runs):
                        print(f"  Run {i}: '{run.text}'")

                    # Case 1: If "HELD" is at the end of the paragraph with no date, add the date
                    if paragraph.text.strip().endswith("HELD"):
                        print("Found 'HELD' at the end of paragraph with no date")
                        # Find the run containing "HELD"
                        for run in paragraph.runs:
                            if "HELD" in run.text and run.text.strip().endswith("HELD"):
                                run.text = run.text + " " + meeting_date
                                print(f"Added date after 'HELD': '{run.text}'")
                                return True

                    # Case 2: Check if paragraph contains "HELD" but no month
                    has_month = any(month in paragraph.text.upper() for month in
                                  ["JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE",
                                   "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"])

                    if not has_month:
                        print("Found 'HELD' but no month in paragraph")
                        # Find the run containing "HELD"
                        for i, run in enumerate(paragraph.runs):
                            if "HELD" in run.text:
                                # If "HELD" is at the end of this run, add the date
                                if run.text.strip().endswith("HELD"):
                                    run.text = run.text + " " + meeting_date
                                    print(f"Added date after 'HELD': '{run.text}'")
                                    return True
                                # Otherwise, check if there's a next run to replace
                                elif i < len(paragraph.runs) - 1:
                                    next_run = paragraph.runs[i+1]
                                    # If next run is empty or just whitespace, add our date
                                    if not next_run.text.strip():
                                        next_run.text = " " + meeting_date
                                        print(f"Added date in next run: '{next_run.text}'")
                                        return True
                                # If we can't add after, try to add it in the same run after "HELD"
                                else:
                                    parts = run.text.split("HELD")
                                    run.text = parts[0] + "HELD " + meeting_date
                                    print(f"Added date after 'HELD' in same run: '{run.text}'")
                                    return True

                    # Case 3: Standard case - replace existing date
                    if has_month:
                        print(f"Found 'HELD' with month in paragraph")
                        # We need to preserve formatting, so we'll replace text in each run
                        for run in paragraph.runs:
                            if "HELD" in run.text:
                                parts = run.text.split("HELD")
                                run.text = parts[0] + "HELD " + meeting_date
                                print(f"Replaced with: '{run.text}'")
                                return True

                return False

            # Search in main document paragraphs
            date_replaced = False
            for paragraph in doc.paragraphs:
                if replace_date_in_paragraph(paragraph):
                    date_replaced = True
                    break

            # If not found in main document, search in tables
            if not date_replaced:
                for table in doc.tables:
                    for row in table.rows:
                        for cell in row.cells:
                            for paragraph in cell.paragraphs:
                                if replace_date_in_paragraph(paragraph):
                                    date_replaced = True
                                    break
                            if date_replaced:
                                break
                        if date_replaced:
                            break
                    if date_replaced:
                        break

            # If not found in main document or tables, search in headers and footers
            if not date_replaced:
                for section in doc.sections:
                    # Check header
                    if section.header:
                        for paragraph in section.header.paragraphs:
                            if replace_date_in_paragraph(paragraph):
                                date_replaced = True
                                break

                        # Check header tables
                        if not date_replaced:
                            for table in section.header.tables:
                                for row in table.rows:
                                    for cell in row.cells:
                                        for paragraph in cell.paragraphs:
                                            if replace_date_in_paragraph(paragraph):
                                                date_replaced = True
                                                break
                                        if date_replaced:
                                            break
                                    if date_replaced:
                                        break
                                if date_replaced:
                                    break

                    # Check footer
                    if not date_replaced and section.footer:
                        for paragraph in section.footer.paragraphs:
                            if replace_date_in_paragraph(paragraph):
                                date_replaced = True
                                break

                        # Check footer tables
                        if not date_replaced:
                            for table in section.footer.tables:
                                for row in table.rows:
                                    for cell in row.cells:
                                        for paragraph in cell.paragraphs:
                                            if replace_date_in_paragraph(paragraph):
                                                date_replaced = True
                                                break
                                        if date_replaced:
                                            break
                                    if date_replaced:
                                        break
                                if date_replaced:
                                    break

            # If we still haven't found the date, try to find any paragraph containing "HELD" and add the date
            if not date_replaced:
                print("Looking for any paragraph containing 'HELD'")
                for paragraph in doc.paragraphs:
                    if "HELD" in paragraph.text:
                        print(f"Found paragraph with 'HELD': '{paragraph.text}'")
                        # Find the run containing "HELD"
                        for i, run in enumerate(paragraph.runs):
                            if "HELD" in run.text:
                                # Add the date after "HELD" regardless of what's there
                                parts = run.text.split("HELD")
                                run.text = parts[0] + "HELD " + meeting_date
                                print(f"Added date after 'HELD': '{run.text}'")
                                date_replaced = True
                                break
                        if date_replaced:
                            break

            # If we still haven't found the date, try a more direct approach
            if not date_replaced:
                print("Using direct XML modification approach")
                try:
                    # Save the document to a temporary file
                    temp_docx = template_path + ".temp.docx"
                    doc.save(temp_docx)

                    # Use zipfile to extract and modify the document.xml file
                    import zipfile
                    import re

                    # Create a new zip file for the modified document
                    temp_dir = os.path.dirname(temp_docx)
                    modified_docx = os.path.join(temp_dir, "modified.docx")

                    with zipfile.ZipFile(temp_docx, 'r') as zip_ref:
                        # Create a new zip file
                        with zipfile.ZipFile(modified_docx, 'w') as new_zip:
                            # Process each file in the zip
                            for item in zip_ref.infolist():
                                # Read the file content
                                content = zip_ref.read(item.filename)

                                # If it's an XML file, try to replace the date
                                if item.filename.endswith('.xml'):
                                    try:
                                        # Convert bytes to string
                                        content_str = content.decode('utf-8')

                                        # First, look for "HELD" followed by a date
                                        date_pattern = r'HELD\s+[A-Z]+\s+\d+,\s+\d{4}'
                                        if re.search(date_pattern, content_str, re.IGNORECASE):
                                            print(f"Found date pattern in XML: {re.search(date_pattern, content_str, re.IGNORECASE).group(0)}")
                                            # Replace the date
                                            modified_content = re.sub(
                                                date_pattern,
                                                f"HELD {meeting_date}",
                                                content_str,
                                                flags=re.IGNORECASE
                                            )
                                            content = modified_content.encode('utf-8')
                                            date_replaced = True
                                            print(f"Replaced date in {item.filename}")
                                        else:
                                            # Look for "HELD" at the end of a line or followed by whitespace/punctuation
                                            held_pattern = r'HELD(\s*$|\s*[,.;:]|\s+(?![A-Za-z]))'
                                            held_match = re.search(held_pattern, content_str, re.IGNORECASE)
                                            if held_match:
                                                print(f"Found 'HELD' without date in XML: {held_match.group(0)}")
                                                # Replace "HELD" with "HELD" followed by the date
                                                modified_content = re.sub(
                                                    held_pattern,
                                                    f"HELD {meeting_date}",
                                                    content_str,
                                                    flags=re.IGNORECASE
                                                )
                                                content = modified_content.encode('utf-8')
                                                date_replaced = True
                                                print(f"Added date after 'HELD' in {item.filename}")
                                            else:
                                                print(f"No 'HELD' pattern found in {item.filename}")
                                    except Exception as xml_error:
                                        print(f"Error processing XML file {item.filename}: {str(xml_error)}")

                                # Write the file to the new zip
                                new_zip.writestr(item, content)

                    # If we modified the document, use the modified version
                    if date_replaced:
                        # Load the modified document
                        doc = Document(modified_docx)
                        print("Loaded modified document with replaced date")

                    # Clean up temporary files
                    if os.path.exists(temp_docx):
                        os.remove(temp_docx)

                except Exception as e:
                    print(f"Error in direct XML modification: {str(e)}")

            print(f"Date replacement successful: {date_replaced}")

        # We'll keep the document's original margins and instead adjust the content positioning

        # Add title with proper spacing
        title_paragraph = doc.add_paragraph()
        title_paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_paragraph.space_before = Pt(12)
        title_paragraph.space_after = Pt(12)
        # No left indent for title as it's centered
        title_run = title_paragraph.add_run(title.upper())
        title_run.bold = True
        title_run.font.size = Pt(16)

        # Add date and time with proper spacing
        if date_time:
            date_paragraph = doc.add_paragraph()
            date_paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
            date_paragraph.space_before = Pt(6)
            date_paragraph.space_after = Pt(12)
            # Add left indent to avoid overlapping with side elements
            date_paragraph.paragraph_format.left_indent = Cm(4.0)
            date_run = date_paragraph.add_run(f"Date and Time: {date_time}")
            date_run.bold = True
            date_run.font.size = Pt(12)

        # Add attendees with proper spacing
        if attendees:
            attendees_heading = doc.add_paragraph()
            attendees_heading.space_before = Pt(12)
            attendees_heading.space_after = Pt(6)
            # Add left indent to avoid overlapping with side elements
            attendees_heading.paragraph_format.left_indent = Cm(4.0)
            attendees_heading.add_run("Present Faculty Attendees:").bold = True

            for attendee in attendees:
                attendee_paragraph = doc.add_paragraph()
                attendee_paragraph.space_before = Pt(0)
                attendee_paragraph.space_after = Pt(3)
                # Add left indent to avoid overlapping with side elements plus bullet indentation
                attendee_paragraph.paragraph_format.left_indent = Cm(4.5)
                attendee_paragraph.add_run("• " + attendee)

        # Add action items with proper spacing
        if action_items:
            action_heading = doc.add_paragraph()
            action_heading.space_before = Pt(12)
            action_heading.space_after = Pt(6)
            # Add left indent to avoid overlapping with side elements
            action_heading.paragraph_format.left_indent = Cm(4.0)
            action_heading.add_run("Action Items:").bold = True

            for i, item in enumerate(action_items):
                action_paragraph = doc.add_paragraph()
                action_paragraph.space_before = Pt(0)
                action_paragraph.space_after = Pt(3)
                # Add left indent to avoid overlapping with side elements plus numbering indentation
                action_paragraph.paragraph_format.left_indent = Cm(4.5)
                action_paragraph.add_run(f"{i+1}. {item}")

        # Add minutes content with proper spacing
        minutes_heading = doc.add_paragraph()
        minutes_heading.space_before = Pt(12)
        minutes_heading.space_after = Pt(6)
        # Add left indent to avoid overlapping with side elements
        minutes_heading.paragraph_format.left_indent = Cm(4.0)
        minutes_heading.add_run("Generated Minutes of the Meeting:").bold = True

        # Add opening statement if available
        if opening_statement:
            # Remove names in square brackets from opening statement
            import re
            opening_statement = re.sub(r'\[[^\]]+\]', '', opening_statement)

            # Add the opening statement as a paragraph with different formatting
            opening_para = doc.add_paragraph()
            opening_para.space_before = Pt(6)
            opening_para.space_after = Pt(12)  # Extra space after opening statement
            opening_para.paragraph_format.left_indent = Cm(4.0)

            # Create a run with Calibri font, size 11, italic formatting (same as content)
            opening_run = opening_para.add_run(opening_statement.strip())
            opening_run.font.name = "Calibri"
            opening_run.font.size = Pt(11)
            opening_run.italic = True

            # No separator between opening statement and content

        # Split content by lines and add each as a paragraph with proper spacing
        if content:
            for line in content.split('\n'):
                if line.strip():
                    content_paragraph = doc.add_paragraph()
                    content_paragraph.space_before = Pt(3)
                    content_paragraph.space_after = Pt(3)
                    # Add left indent to avoid overlapping with side elements
                    content_paragraph.paragraph_format.left_indent = Cm(4.0)
                    # Create a run with Calibri font, size 11, and italic formatting
                    run = content_paragraph.add_run(line.strip())
                    run.font.name = "Calibri"
                    run.font.size = Pt(11)
                    run.italic = True

        # Save the document
        doc.save(output_path)
        print(f"Document saved to {output_path}")
        return 0

    except Exception as e:
        print(f"Error generating DOCX: {str(e)}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
