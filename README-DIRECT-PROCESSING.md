# Direct Python Processing for Meeting Assistant

This document explains how to use the direct Python processing feature, which allows you to process audio files without requiring the Flask server.

## Overview

The Meeting Assistant application now supports two modes of operation:

1. **Flask Mode**: The original mode that uses a Flask server for audio processing
2. **Direct Mode**: A new mode that calls Python directly from Node.js

The direct mode simplifies the architecture by removing the need for a separate Flask server while maintaining all the powerful audio processing capabilities.

## Setup

1. Make sure you have all the required Python dependencies installed:
   ```
   pip install -r python/flask_requirements.txt
   ```

2. Ensure FFmpeg is installed and accessible (required for video/audio conversion)

## Running in Direct Mode

1. Start the Node.js server with direct Python processing:
   ```
   start-server-no-flask.bat
   ```

2. Start the React frontend:
   ```
   start-frontend.bat
   ```

3. Use the application as normal - the frontend interface remains the same

## How It Works

In direct mode:

1. The Node.js server receives uploaded files
2. It calls the Python script (`direct_process.py`) directly using Node.js child_process
3. The Python script processes the audio and returns the results
4. The Node.js server sends the results back to the frontend

## Testing Direct Processing

You can test the direct processing with a sample file:

```
test-direct-process.bat path/to/your/audio/file.mp3
```

This will process the file and output the results directly to the console.

## Advantages of Direct Mode

- Simpler architecture (no need to run and maintain a separate Flask server)
- Reduced latency (no HTTP overhead between Node.js and Flask)
- Same powerful audio processing capabilities
- Same frontend user experience

## Troubleshooting

If you encounter issues:

1. Check the logs in `direct_process.log`
2. Ensure Python is in your PATH
3. Verify that all Python dependencies are installed
4. Make sure FFmpeg is installed and accessible

## Reverting to Flask Mode

If needed, you can still use the original Flask mode:

1. Start the Flask server:
   ```
   start-flask.bat
   ```

2. Start the Node.js server:
   ```
   start-server.bat
   ```

3. Start the React frontend:
   ```
   start-frontend.bat
   ```
