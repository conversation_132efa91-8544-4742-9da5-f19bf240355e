"""
Very basic test script
"""
print("Testing basic functionality...")

# Define a simple function for transcript cleaning
def basic_transcript_cleaning(transcript):
    """Basic transcript cleaning function"""
    # Remove filler words
    cleaned = transcript.replace("um", "").replace("uh", "")
    return cleaned

# Create a sample transcript
sample_transcript = """
Speaker 1: Good morning everyone um I would like to start the meeting.
Speaker 2: Yes, I think we should uh focus on the deliverables.
"""

# Test basic cleaning
print("\nTesting basic transcript cleaning...")
basic_cleaned = basic_transcript_cleaning(sample_transcript)
print("\nBasic cleaned transcript:")
print("-" * 50)
print(basic_cleaned)
print("-" * 50)

print("Test completed successfully!")
