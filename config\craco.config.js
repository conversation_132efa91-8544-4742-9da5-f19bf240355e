const path = require('path');
const webpack = require('webpack');

module.exports = {
  webpack: {
    configure: (webpackConfig, { env, paths }) => {
      // Update webpack-dev-server configuration
      if (webpackConfig.devServer) {
        // Remove deprecated options
        delete webpackConfig.devServer.onBeforeSetupMiddleware;
        delete webpackConfig.devServer.onAfterSetupMiddleware;

        // Add the new setupMiddlewares option
        webpackConfig.devServer.setupMiddlewares = function(middlewares, devServer) {
          // You can add your custom middleware here if needed
          return middlewares;
        };
      }

      // Add Node.js polyfills
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        "vm": require.resolve("vm-browserify"),
        "buffer": require.resolve("buffer/"),
        "stream": require.resolve("stream-browserify"),
        "path": require.resolve("path-browserify"),
        "util": require.resolve("util/"),
        "process": require.resolve("process/browser"),
      };

      // Add plugins for polyfills
      webpackConfig.plugins.push(
        new webpack.ProvidePlugin({
          Buffer: ['buffer', 'Buffer'],
          process: 'process/browser',
        })
      );

      return webpackConfig;
    },
  },
};
