"""
Optimized processing script for audio and video files.
This version focuses on performance and uses GPU acceleration when available.
"""

import os
import sys
import json
import time
import subprocess
import traceback
import tempfile
from datetime import <PERSON><PERSON><PERSON>

def sanitize_json_data(data):
    """
    Recursively sanitize data to ensure it can be safely encoded as JSON.
    Handles problematic characters and non-serializable objects.

    Args:
        data: The data to sanitize (can be dict, list, str, or other types)

    Returns:
        The sanitized data that can be safely JSON-encoded
    """
    if isinstance(data, dict):
        return {k: sanitize_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [sanitize_json_data(item) for item in data]
    elif isinstance(data, str):
        # Replace problematic characters with safe alternatives
        # This handles Unicode characters that might cause encoding issues
        return data.encode('ascii', 'replace').decode('ascii')
    elif isinstance(data, (int, float, bool, type(None))):
        # These types are JSON-serializable as is
        return data
    else:
        # Convert other types to strings
        return str(data)

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import GPU configuration
try:
    from gpu_config import setup_environment, get_system_info
    USE_GPU_CONFIG = True
except ImportError:
    USE_GPU_CONFIG = False
    print("GPU config not found, using default settings")

# Set up environment with optimized settings
if USE_GPU_CONFIG:
    settings = setup_environment()
    print(f"Environment set up with optimized settings: {settings}")
    system_info = get_system_info()
    print(f"System information: {system_info}")

# Import progress utilities
from progress_utils import send_progress, ProgressReporter

# Import Pipeline Phase 1
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase1'))
try:
    from PHASE1 import AcademicTranscriptionPipeline
    print("Successfully loaded PHASE1")
except ImportError as e:
    print(f"Error importing PHASE1: {str(e)}")
    print("Make sure the Pipeline Phase1 directory exists and contains PHASE1.py")
    sys.exit(1)

# Import Pipeline Phase 2
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase2'))

# Try to import Phase2V2 first, fall back to original Phase2 if not available
phase2v2_available = False
phase2_available = False

try:
    # First try to import Phase2V2
    from Phase2V2 import process_meeting_transcript_enhanced as phase2v2_process
    print("Successfully loaded Phase2V2")
    phase2v2_available = True

    def process_transcript(transcript, include_executive_summary=True):
        """
        Process transcript using Phase2V2.

        Args:
            transcript (str): The transcript text
            include_executive_summary (bool): Whether to include executive summary

        Returns:
            str: Formatted meeting minutes
        """
        # Call Phase2V2's function with the appropriate parameters
        # Set enhance_transcript to False to avoid potential memory issues
        print("Using Phase2V2 for processing...")
        return phase2v2_process(transcript, include_executive_summary=include_executive_summary, enhance_transcript=False)

except ImportError as e:
    # If there's an error with Phase2V2, try the original Phase2
    print(f"Phase2V2 not available: {str(e)}")
    print("Trying original Phase2...")

    try:
        from PHASE2 import process_meeting_transcript_enhanced
        print("Successfully loaded original Phase2")
        phase2_available = True

        def process_transcript(transcript, include_executive_summary=True):
            """
            Process transcript using original Phase2.

            Args:
                transcript (str): The transcript text
                include_executive_summary (bool): Whether to include executive summary

            Returns:
                str: Formatted meeting minutes
            """
            print("Using original Phase2 for processing...")
            return process_meeting_transcript_enhanced(transcript, include_executive_summary=include_executive_summary)

    except ImportError as e2:
        print(f"Original Phase2 not available: {str(e2)}")
        print("No Phase2 module available. Cannot process transcripts.")

# Verify that at least one Phase2 module is available
if not phase2v2_available and not phase2_available:
    print("ERROR: Neither Phase2V2 nor Phase2 could be imported. Cannot continue.")
    sys.exit(1)

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def convert_to_wav(input_file, output_file=None):
    """
    Convert audio/video file to WAV format optimized for speech recognition.

    Args:
        input_file (str): Path to input file
        output_file (str, optional): Path to output WAV file

    Returns:
        str: Path to WAV file
    """
    # Get file extension
    file_ext = os.path.splitext(input_file)[1].lower()

    # Skip conversion if already WAV
    if file_ext.lower() == '.wav':
        print(f"File is already in WAV format: {input_file}")
        return input_file

    # Create output filename in temp directory if not provided
    if output_file is None:
        output_file = os.path.join(
            tempfile.gettempdir(),
            f"converted_{int(time.time())}.wav"
        )

    try:
        # Use ffmpeg.bat to convert
        ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'ffmpeg.bat')

        # Check if ffmpeg.bat exists, otherwise use direct ffmpeg command
        if not os.path.exists(ffmpeg_path):
            print(f"ffmpeg.bat not found at {ffmpeg_path}, trying direct ffmpeg command")
            ffmpeg_cmd = ['ffmpeg']
        else:
            print(f"Using ffmpeg.bat at {ffmpeg_path}")
            ffmpeg_cmd = [ffmpeg_path]

        # Add conversion parameters - optimized for speech recognition
        ffmpeg_cmd.extend([
            '-i', input_file,
            '-ar', '16000',  # 16kHz sample rate (optimal for speech recognition)
            '-ac', '1',      # Mono
            '-c:a', 'pcm_s16le',  # 16-bit PCM
            '-y',            # Overwrite output file
            output_file
        ])

        # Execute conversion
        file_size_mb = os.path.getsize(input_file) / (1024 * 1024)
        send_progress("Converting", 0, f"Converting {file_ext.upper()} file ({file_size_mb:.2f} MB) to WAV format")

        # Use standard subprocess without creationflags to avoid issues
        print(f"Executing FFmpeg command: {' '.join(ffmpeg_cmd)}")
        process = subprocess.Popen(
            ffmpeg_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )

        # Wait for process to complete
        _, stderr = process.communicate()  # We only need stderr for error messages

        # Check if conversion was successful
        if process.returncode != 0:
            error_msg = stderr.decode()
            print(f"FFmpeg conversion failed: {error_msg}")
            send_progress("Error", 0, f"FFmpeg conversion failed: {error_msg[:100]}...")
            raise Exception(f"FFmpeg conversion failed with code {process.returncode}")

        # Verify the output file exists
        if not os.path.exists(output_file):
            raise Exception(f"Output file not created: {output_file}")

        output_size_mb = os.path.getsize(output_file) / (1024 * 1024)
        send_progress("Converting", 100, f"Conversion complete: {file_ext} → WAV ({output_size_mb:.2f} MB)")
        print(f"Successfully converted {input_file} to {output_file}")

        return output_file

    except Exception as e:
        print(f"Error converting file: {str(e)}")
        traceback.print_exc()
        raise

def process_file(input_file, title, description, user_id, media_duration=None):
    """
    Process audio/video file to generate transcript and minutes.

    Args:
        input_file (str): Path to input file
        title (str): Title of the meeting
        description (str): Description or attendance information
        user_id (str): User ID
        media_duration (float, optional): Duration of media in seconds

    Returns:
        dict: Processing results
    """
    start_time = time.time()
    print(f"\n=== STARTING PROCESSING FOR: {title} ===")

    # Initialize the enhanced progress reporter
    progress_reporter = ProgressReporter("Transcribing", total_steps=4, update_interval=2.0)

    # Set file information for better time estimation
    progress_reporter.set_file_info(file_path=input_file, audio_duration=media_duration)

    # Start the progress reporter (begins sending regular updates)
    progress_reporter.start()

    # Log media duration if provided
    if media_duration:
        print(f"Media duration provided: {media_duration} seconds")

    # Create a list to track temporary files for cleanup
    temp_files = []

    try:
        # Step 1: Initialize and prepare audio
        progress_reporter.next_step("Preparing audio file")

        # Convert to WAV format if needed
        progress_reporter.update(5, "Converting audio to WAV format")
        wav_file = convert_to_wav(input_file)

        # Add to temp files list if it's different from the input file
        if wav_file != input_file:
            temp_files.append(wav_file)

        # Initialize Pipeline 1 with optimized settings
        progress_reporter.update(10, "Loading speech recognition and transcription models")
        pipeline1 = AcademicTranscriptionPipeline()
        progress_reporter.update(20, "Models loaded successfully")

        # Step 2: Voice Activity Detection (handled by pipeline)
        progress_reporter.next_step("Voice activity detection")

        # Step 3: Speaker Identification (handled by pipeline)
        # The pipeline will automatically move to this step

        # Step 4: Speech Recognition and Transcription
        progress_reporter.next_step("Speech recognition and transcription")
        progress_reporter.update(60, "Starting audio transcription - this may take several minutes")

        # Process through Pipeline 1 (transcription) - this is the most time-consuming part
        transcript = pipeline1.create_translated_transcript(wav_file)

        # Count speakers and lines
        speakers = set()
        line_count = 0
        for line in transcript:
            line_count += 1
            if ":" in line:
                speaker = line.split(":", 1)[0].strip()
                if speaker and speaker != "Unknown":
                    speakers.add(speaker)

        # Join transcript lines
        transcript_text = "\n".join(transcript)

        # 4. Save transcript to file
        transcript_filename = f"{os.path.splitext(os.path.basename(input_file))[0]}_transcript.txt"
        transcription_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "transcription")

        # Create directory if it doesn't exist
        os.makedirs(transcription_dir, exist_ok=True)

        transcript_file_path = os.path.join(transcription_dir, transcript_filename)
        with open(transcript_file_path, 'w', encoding='utf-8') as f:
            f.write(transcript_text)

        # Save original Tagalog transcript if available
        original_tagalog_file_path = None
        if hasattr(pipeline1, 'original_transcript') and pipeline1.original_transcript:
            # Clean the original transcript
            cleaned_original = []
            for line in pipeline1.original_transcript:
                # Replace any problematic characters
                cleaned_line = line.replace('\\', '/')
                # Remove any control characters
                cleaned_line = ''.join(ch for ch in cleaned_line if ord(ch) >= 32 or ch == '\n')
                cleaned_original.append(cleaned_line)

            # Save original Tagalog transcript
            tagalog_transcript_filename = f"{os.path.splitext(os.path.basename(input_file))[0]}_transcript_tagalog.txt"
            original_tagalog_file_path = os.path.join(transcription_dir, tagalog_transcript_filename)

            with open(original_tagalog_file_path, "w", encoding="utf-8") as f:
                for line in cleaned_original:
                    f.write(line + "\n")

            print(f"Original Tagalog transcript saved to: {original_tagalog_file_path}")

        # Update progress with transcript information
        progress_reporter.update(75, f"Transcript generated with {len(speakers)} speakers and {line_count} lines")
        print(f"Transcript saved to: {transcript_file_path}")

        # Start minutes generation step
        progress_reporter.next_step("Generating meeting minutes")
        progress_reporter.update(80, "Analyzing transcript and generating minutes")

        # Process through Pipeline 2 (minutes generation)
        minutes = process_transcript(transcript_text, include_executive_summary=True)

        # Save minutes to file
        minutes_filename = f"{os.path.splitext(os.path.basename(input_file))[0]}_minutes.md"
        minutes_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "minutes of the meeting")

        # Create directory if it doesn't exist
        os.makedirs(minutes_dir, exist_ok=True)

        # Update progress with finalization
        progress_reporter.update(95, "Saving meeting minutes")

        minutes_file_path = os.path.join(minutes_dir, minutes_filename)
        with open(minutes_file_path, 'w', encoding='utf-8') as f:
            f.write(minutes)

        # Final progress update
        progress_reporter.update(98, "Finalizing results")
        print(f"Minutes saved to: {minutes_file_path}")

        # 7. Prepare result
        processing_time = time.time() - start_time
        result = {
            "status": "success",
            "data": {
                "transcript": transcript_text,
                "minutes": minutes,
                "transcript_file": transcript_file_path,
                "minutes_file": minutes_file_path,
                "speakers": list(speakers),
                "processing_time": processing_time,
                "processing_time_formatted": format_time(processing_time),
                "transcription_dir": transcription_dir,
                "minutes_dir": minutes_dir,
                "job_id": f"job_{int(time.time())}",
                "title": title,
                "description": description,
                "user_id": user_id
            }
        }

        # Add original Tagalog transcript file path if available
        if original_tagalog_file_path and os.path.exists(original_tagalog_file_path):
            result["data"]["original_transcript_file"] = original_tagalog_file_path

        # Report completion
        progress_reporter.update(100, f"Processing completed in {format_time(processing_time)}", force=True)
        print(f"\n=== PROCESSING COMPLETED IN {format_time(processing_time)} ===")

        # Stop the progress reporter
        progress_reporter.stop()

        return result

    except Exception as e:
        # Handle errors
        error_time = time.time() - start_time
        print(f"Error after {format_time(error_time)}: {str(e)}")
        traceback.print_exc()

        # Update progress with error
        if 'progress_reporter' in locals():
            progress_reporter.update(100, f"Processing failed: {str(e)}", force=True)
            progress_reporter.stop()
        else:
            send_progress("Error", 100, f"Processing failed: {str(e)}")

        return {
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc(),
            "processing_time": error_time
        }
    finally:
        # Clean up temporary files
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    print(f"Cleaning up temporary file: {temp_file}")
                    os.remove(temp_file)
            except Exception as cleanup_error:
                print(f"Error cleaning up temporary file {temp_file}: {str(cleanup_error)}")

if __name__ == "__main__":
    # Check if we have enough command line arguments
    if len(sys.argv) < 4:
        print("Usage: python fast_process.py <input_file> <title> <description> [user_id] [media_duration]")
        sys.exit(1)

    # Get command line arguments
    input_file = sys.argv[1]
    title = sys.argv[2]
    description = sys.argv[3]
    user_id = sys.argv[4] if len(sys.argv) > 4 else ""
    media_duration = float(sys.argv[5]) if len(sys.argv) > 5 and sys.argv[5].replace('.', '', 1).isdigit() else None

    # Process the file
    result = process_file(input_file, title, description, user_id, media_duration)

    # Print result as JSON with sanitization to avoid encoding issues
    sanitized_result = sanitize_json_data(result)
    print(json.dumps(sanitized_result, ensure_ascii=True))
