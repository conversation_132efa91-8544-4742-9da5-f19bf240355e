#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for minutes file storage in the main minutes directory
"""

import os
import sys
import time
import json
import argparse

def main():
    print("Testing minutes file storage in the main minutes directory...")
    
    # Create a sample text file
    sample_text = """
Speaker 1: Hello everyone, welcome to our meeting.
Speaker 2: Thank you for having us today.
Speaker 1: Let's discuss the agenda for today.
Speaker 2: I think we should start with the project updates.
Speaker 1: Good idea. Let's begin with the marketing team.
Speaker 3: The marketing campaign is going well. We've seen a 20% increase in engagement.
Speaker 1: That's excellent news. What about the development team?
Speaker 4: We're on track with the new features. The beta testing will start next week.
Speaker 2: That's great progress. Any challenges we should be aware of?
Speaker 4: We're facing some issues with the database optimization, but we're working on it.
Speaker 1: Let me know if you need any additional resources.
Speaker 4: Thank you, we'll keep you updated.
Speaker 1: Let's move on to the next item on the agenda.
"""
    
    # Save the sample text to a file
    test_dir = os.path.dirname(os.path.abspath(__file__))
    sample_file_path = os.path.join(test_dir, "sample_transcript_minutes_test.txt")
    
    with open(sample_file_path, "w", encoding="utf-8") as f:
        f.write(sample_text)
    
    print(f"Created sample transcript file at: {sample_file_path}")
    
    # Process the sample file using simple_text_processor.py
    simple_processor_path = os.path.join(test_dir, "simple_text_processor.py")
    
    # Create output directory
    output_dir = os.path.join(test_dir, "test_output_minutes")
    os.makedirs(output_dir, exist_ok=True)
    
    # Build the command
    cmd = [
        sys.executable,
        simple_processor_path,
        sample_file_path,
        "--language", "english",
        "--title", "Test Minutes Storage",
        "--output_dir", output_dir
    ]
    
    # Run the command
    print(f"Running command: {' '.join(cmd)}")
    start_time = time.time()
    
    import subprocess
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # Print output in real-time
    for line in process.stdout:
        print(line.strip())
    
    # Wait for the process to complete
    process.wait()
    
    # Print any errors
    for line in process.stderr:
        print(f"ERROR: {line.strip()}")
    
    # Check the result
    processing_time = time.time() - start_time
    print(f"Processing completed in {processing_time:.2f} seconds")
    
    # Check if the output files exist
    transcript_dir = os.path.join(output_dir, "transcription")
    minutes_dir = os.path.join(output_dir, "minutes of the meeting")
    main_minutes_dir = 'C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\minutes of the meeting'
    
    # Check the output directory
    if os.path.exists(minutes_dir):
        print(f"Minutes directory created: {minutes_dir}")
        minutes_files = os.listdir(minutes_dir)
        print(f"Minutes files in output directory: {minutes_files}")
    else:
        print(f"ERROR: Minutes directory not created: {minutes_dir}")
    
    # Check the main minutes directory
    if os.path.exists(main_minutes_dir):
        print(f"Main minutes directory exists: {main_minutes_dir}")
        
        # Get the list of files in the main minutes directory
        main_minutes_files = os.listdir(main_minutes_dir)
        
        # Filter files to find those with "Test Minutes Storage" in the name
        test_minutes_files = [f for f in main_minutes_files if "Test Minutes Storage" in f]
        
        if test_minutes_files:
            print(f"Found {len(test_minutes_files)} test minutes files in main directory:")
            for file in test_minutes_files:
                print(f"  - {file}")
                
            # Get the most recent test minutes file
            most_recent_file = max(test_minutes_files, key=lambda f: os.path.getmtime(os.path.join(main_minutes_dir, f)))
            most_recent_path = os.path.join(main_minutes_dir, most_recent_file)
            
            print(f"\nMost recent test minutes file: {most_recent_file}")
            print(f"Created at: {time.ctime(os.path.getmtime(most_recent_path))}")
            
            # Check if the file was created during this test
            file_creation_time = os.path.getmtime(most_recent_path)
            if file_creation_time >= start_time:
                print(f"SUCCESS: Minutes file was created during this test ✓")
            else:
                print(f"WARNING: Minutes file was created before this test started ✗")
                
            # Display the content of the minutes file
            print(f"\nContent of minutes file: {most_recent_path}")
            print("-" * 50)
            with open(most_recent_path, "r", encoding="utf-8") as f:
                content = f.read()
                # Print just the first 500 characters to keep the output manageable
                print(content[:500] + "..." if len(content) > 500 else content)
            print("-" * 50)
        else:
            print(f"ERROR: No test minutes files found in main directory ✗")
    else:
        print(f"ERROR: Main minutes directory does not exist: {main_minutes_dir} ✗")
    
    # Clean up
    try:
        os.remove(sample_file_path)
        print(f"Removed sample file: {sample_file_path}")
    except Exception as e:
        print(f"Error removing sample file: {e}")
    
    print("Test completed.")

if __name__ == "__main__":
    main()
