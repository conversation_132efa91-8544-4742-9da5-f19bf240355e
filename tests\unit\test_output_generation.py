"""
Test script to verify output generation.
This script tests the output generation process without processing a full file.
"""

import os
import sys
import json
import time
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the sanitize_json_data function
from fast_process import sanitize_json_data

def test_output_generation():
    """Test the output generation process."""
    # First, ensure output directories exist
    from ensure_output_dirs import ensure_output_dirs
    dirs = ensure_output_dirs()
    
    # Create a sample transcript
    transcript = [
        "Speaker 1: Hello, this is a test transcript.",
        "Speaker 2: This is used to test the output generation.",
        "Speaker 1: We want to make sure the JSON output is properly formatted.",
        "Speaker 3: And that the files are created in the right locations."
    ]
    
    # Create a sample minutes
    minutes = """# Meeting Minutes

## Executive Summary

This is a test meeting to verify output generation.

## Discussion Topics

### Testing

The team discussed testing the output generation process.

## Action Items

1. **Team** to verify output generation works correctly
"""
    
    # Create a unique job ID
    job_id = f"test_{int(time.time())}"
    
    # Create file paths
    transcript_dir = dirs['transcription']
    minutes_dir = dirs['minutes']
    
    transcript_file = os.path.join(transcript_dir, f"{job_id}_transcript.txt")
    minutes_file = os.path.join(minutes_dir, f"{job_id}_minutes.md")
    
    # Write the transcript file
    with open(transcript_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(transcript))
    
    # Write the minutes file
    with open(minutes_file, 'w', encoding='utf-8') as f:
        f.write(minutes)
    
    # Create the result object
    result = {
        "status": "success",
        "data": {
            "transcript": '\n'.join(transcript),
            "minutes": minutes,
            "transcript_file": transcript_file,
            "minutes_file": minutes_file,
            "speakers": ["Speaker 1", "Speaker 2", "Speaker 3"],
            "processing_time": 10.5,
            "processing_time_formatted": "0:00:10",
            "transcription_dir": transcript_dir,
            "minutes_dir": minutes_dir,
            "job_id": job_id,
            "title": "Test Meeting",
            "description": "Test Description",
            "user_id": "test_user"
        }
    }
    
    # Add some problematic characters to test sanitization
    result["data"]["transcript"] += "\nSpeaker 4: This contains some problematic characters: 😊 ñ é ü"
    
    # Sanitize the result
    sanitized_result = sanitize_json_data(result)
    
    # Convert to JSON
    json_result = json.dumps(sanitized_result, ensure_ascii=True, indent=2)
    
    # Print the JSON result
    print(json_result)
    
    # Write the JSON result to a file
    json_file = os.path.join(dirs['processed'], f"{job_id}_result.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        f.write(json_result)
    
    print(f"\nFiles created:")
    print(f"  Transcript: {transcript_file}")
    print(f"  Minutes: {minutes_file}")
    print(f"  JSON: {json_file}")
    
    return {
        "transcript_file": transcript_file,
        "minutes_file": minutes_file,
        "json_file": json_file
    }

if __name__ == "__main__":
    print("Testing output generation...")
    files = test_output_generation()
    print("Done.")
