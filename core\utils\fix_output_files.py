"""
Fix existing output files that might have encoding issues.
This script scans the output directories and fixes any files with encoding issues.
"""

import os
import sys
import json
import glob

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the sanitize_json_data function
from fast_process import sanitize_json_data

def fix_output_files():
    """
    Scan output directories and fix any files with encoding issues.
    Returns a list of fixed files.
    """
    # First, ensure output directories exist
    from ensure_output_dirs import ensure_output_dirs
    dirs = ensure_output_dirs()
    
    fixed_files = []
    
    # Fix transcript files
    transcript_files = glob.glob(os.path.join(dirs['transcription'], '*.txt'))
    for file_path in transcript_files:
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            # Sanitize the content
            sanitized_content = sanitize_json_data(content)
            
            # Write the sanitized content back to the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(sanitized_content)
            
            fixed_files.append(file_path)
            print(f"Fixed transcript file: {file_path}")
        except Exception as e:
            print(f"Error fixing transcript file {file_path}: {e}")
    
    # Fix minutes files
    minutes_files = glob.glob(os.path.join(dirs['minutes'], '*.md'))
    for file_path in minutes_files:
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            # Sanitize the content
            sanitized_content = sanitize_json_data(content)
            
            # Write the sanitized content back to the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(sanitized_content)
            
            fixed_files.append(file_path)
            print(f"Fixed minutes file: {file_path}")
        except Exception as e:
            print(f"Error fixing minutes file {file_path}: {e}")
    
    # Fix JSON files
    json_files = glob.glob(os.path.join(dirs['processed'], '*.json'))
    for file_path in json_files:
        try:
            # Read the file
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            # Parse the JSON
            try:
                data = json.loads(content)
                
                # Sanitize the data
                sanitized_data = sanitize_json_data(data)
                
                # Write the sanitized data back to the file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(sanitized_data, f, ensure_ascii=True, indent=2)
                
                fixed_files.append(file_path)
                print(f"Fixed JSON file: {file_path}")
            except json.JSONDecodeError:
                print(f"Error parsing JSON file {file_path}")
        except Exception as e:
            print(f"Error fixing JSON file {file_path}: {e}")
    
    return fixed_files

if __name__ == "__main__":
    print("Fixing output files...")
    fixed_files = fix_output_files()
    print(f"Fixed {len(fixed_files)} files.")
