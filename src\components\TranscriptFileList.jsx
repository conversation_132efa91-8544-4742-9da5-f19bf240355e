import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import backIcon from '../assets/back.png';

const TranscriptFileList = () => {
    const [transcriptFiles, setTranscriptFiles] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const navigate = useNavigate();

    // Table header style
    const tableHeaderStyle = {
        textAlign: 'left',
        padding: '15px 20px',
        color: '#034FAF',
        fontWeight: '700',
        fontSize: '14px',
        letterSpacing: '0.5px',
        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
    };

    // Load transcript files from the server
    const loadTranscriptFiles = async () => {
        try {
            setLoading(true);
            setError(null);

            // Use the list-files endpoint to get all files in the transcription directory
            const response = await fetch('http://localhost:3001/api/list-files?path=python/transcription');

            if (!response.ok) {
                throw new Error(`Failed to fetch transcript files: ${response.status}`);
            }

            const data = await response.json();
            console.log('Transcript files:', data);

            if (data.status === 'success' && data.fileDetails) {
                // Filter for only transcript files
                const transcripts = data.fileDetails.filter(file =>
                    file.name.includes('_transcript') && file.name.endsWith('.txt')
                );

                // Sort by modification date (newest first)
                transcripts.sort((a, b) => new Date(b.modified) - new Date(a.modified));

                setTranscriptFiles(transcripts);
            } else {
                // Try with absolute path as fallback
                try {
                    const absoluteResponse = await fetch('http://localhost:3001/api/list-files?path=C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription');

                    if (absoluteResponse.ok) {
                        const absoluteData = await absoluteResponse.json();
                        console.log('Transcript files (absolute path):', absoluteData);

                        if (absoluteData.status === 'success' && absoluteData.fileDetails) {
                            // Filter for only transcript files
                            const transcripts = absoluteData.fileDetails.filter(file =>
                                file.name.includes('_transcript') && file.name.endsWith('.txt')
                            );

                            // Sort by modification date (newest first)
                            transcripts.sort((a, b) => new Date(b.modified) - new Date(a.modified));

                            setTranscriptFiles(transcripts);
                            return;
                        }
                    }
                } catch (error) {
                    console.error('Error with absolute path fallback:', error);
                }

                setTranscriptFiles([]);
            }
        } catch (error) {
            console.error('Error loading transcript files:', error);
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    // Load files on component mount
    useEffect(() => {
        loadTranscriptFiles();
    }, []);

    // Handle file click - view the transcript
    const handleFileClick = async (file) => {
        try {
            // Fetch the file content
            const response = await fetch(`http://localhost:3001/api/file?path=${encodeURIComponent(file.path)}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch file content: ${response.status}`);
            }

            const content = await response.text();

            // Parse the file name to extract information
            const fileName = file.name;
            // Determine if this is a Tagalog file based on the file name or type property
            const isTagalog = file.type === 'Tagalog' || fileName.includes('_tagalog');
            console.log('File type:', file.type, 'Is Tagalog:', isTagalog);
            const timestamp = fileName.split('-')[0];

            // Create a title from the file name
            let title = fileName.replace('_transcript_tagalog.txt', '').replace('_transcript.txt', '');
            if (title.includes('-')) {
                title = title.split('-').slice(1).join('-'); // Remove timestamp prefix
            }
            title = title.replace(/_/g, ' ').trim(); // Replace underscores with spaces

            // Capitalize first letter of each word
            title = title.split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                .join(' ');

            // Create a transcript object
            const transcript = {
                id: timestamp, // Use timestamp as ID
                job_id: timestamp,
                title: title,
                transcript: isTagalog ? null : content, // English content
                original_transcript: isTagalog ? content : null, // Tagalog content
                original_transcript_file: file.path, // Store file path for both types
                timestamp: new Date(parseInt(timestamp)).toISOString(),
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                file_path: file.path,
                file_name: fileName,
                is_tagalog: isTagalog // Add explicit flag for language type
            };

            // If this is a Tagalog file, try to find the corresponding English file and vice versa
            const baseFileName = fileName.replace('_tagalog.txt', '.txt');
            const tagalogFileName = baseFileName.replace('.txt', '_tagalog.txt');

            // Find the corresponding file in the list
            const correspondingFile = transcriptFiles.find(f =>
                isTagalog ? f.name === baseFileName : f.name === tagalogFileName
            );

            if (correspondingFile) {
                console.log('Found corresponding file:', correspondingFile.name);

                // Fetch the content of the corresponding file
                try {
                    const correspondingResponse = await fetch(`http://localhost:3001/api/file?path=${encodeURIComponent(correspondingFile.path)}`);

                    if (correspondingResponse.ok) {
                        const correspondingContent = await correspondingResponse.text();

                        // Update the transcript object with the corresponding content
                        if (isTagalog) {
                            transcript.transcript = correspondingContent; // English content
                        } else {
                            transcript.original_transcript = correspondingContent; // Tagalog content
                            transcript.original_transcript_file = correspondingFile.path;
                        }
                    }
                } catch (error) {
                    console.error('Error fetching corresponding file:', error);
                }
            }

            // Make sure the language type is explicitly set
            transcript.type = isTagalog ? 'Tagalog' : 'English';

            // Save to localStorage
            localStorage.setItem('currentTranscript', JSON.stringify(transcript));
            console.log('Saved transcript with language type:', transcript.type);

            // Navigate to the transcript view
            navigate('/transcription/current');
        } catch (error) {
            console.error('Error handling file click:', error);
            alert(`Error loading file: ${error.message}`);
        }
    };

    // Format file size for display
    const formatFileSize = (bytes) => {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    };

    // Format date for display
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        try {
            const date = new Date(dateString);
            // Check if date is valid
            if (isNaN(date.getTime())) return 'N/A';

            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'N/A';
        }
    };

    // Group files by base name (without _tagalog suffix)
    const groupFilesByBaseName = (files) => {
        const groups = {};

        files.forEach(file => {
            // Extract base name by removing _tagalog suffix if present
            let baseName = file.name;
            if (baseName.includes('_transcript_tagalog.txt')) {
                baseName = baseName.replace('_transcript_tagalog.txt', '_transcript.txt');
            }

            if (!groups[baseName]) {
                groups[baseName] = [];
            }
            groups[baseName].push(file);
        });

        return Object.values(groups);
    };

    // Group files by their base names
    const groupedFiles = groupFilesByBaseName(transcriptFiles);

    return (
        <div style={{
            display: 'flex',
            height: '100vh',
            fontFamily: 'Montserrat, sans-serif',
            position: 'relative',
            overflow: 'hidden',
        }}>
            {/* Background Pattern */}
            <div className="background-pattern" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `
                    linear-gradient(45deg, rgba(3, 79, 175, 0.1), rgba(87, 215, 226, 0.1)),
                    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23034FAF' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
                `,
                animation: 'backgroundShift 30s linear infinite',
                zIndex: 0,
            }} />

            {/* Gradient Overlay */}
            <div className="gradient-overlay" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(3, 79, 175, 0.25) 100%)',
                zIndex: 1,
            }} />

            <Sidebar style={{ position: 'relative', zIndex: 2 }} />

            <div style={{
                flex: 1,
                padding: '30px',
                height: '100vh',
                overflow: 'auto',
                position: 'relative',
                zIndex: 2,
            }}>
                {/* Header */}
                <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '25px',
                }}>
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                    }}>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            padding: '12px',
                            borderRadius: '50%',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                        }}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'scale(1.05)';
                            e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'scale(1)';
                            e.currentTarget.style.background = 'transparent';
                        }}
                        onClick={() => window.history.back()}
                        >
                            <img
                                src={backIcon}
                                alt="back"
                                style={{
                                    width: '28px',
                                    height: '28px',
                                    transition: 'transform 0.3s ease',
                                    cursor: 'pointer',
                                }}
                            />
                        </div>
                        <h1 style={{
                            margin: 0,
                            fontSize: "24px",
                            fontWeight: "900",
                            color: '#034FAF',
                            letterSpacing: '0.5px',
                            cursor: 'default',
                            textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
                        }}>ALL TRANSCRIPTIONS</h1>
                    </div>

                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '15px',
                    }}>
                        <button
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '8px',
                                padding: '10px 15px',
                                background: 'rgba(3, 79, 175, 0.1)',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease',
                                color: '#034FAF',
                                fontWeight: '600',
                                fontSize: '14px',
                            }}
                            onClick={loadTranscriptFiles}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.background = 'rgba(3, 79, 175, 0.2)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
                            }}
                        >
                            <svg
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                                style={{
                                    marginRight: '5px'
                                }}
                            >
                                <path
                                    d="M4 4V9H4.58152M19.9381 11C19.446 7.05369 16.0796 4 12 4C8.64262 4 5.76829 6.06817 4.58152 9M4.58152 9H9M20 20V15H19.4185M19.4185 15C18.2317 17.9318 15.3574 20 12 20C7.92038 20 4.55399 16.9463 4.06189 13M19.4185 15H15"
                                    stroke="#034FAF"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                />
                            </svg>
                            Refresh
                        </button>
                    </div>
                </div>

                {/* Table Container */}
                <div style={{
                    background: 'rgba(255, 255, 255, 0.15)',
                    borderRadius: '15px',
                    padding: '30px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.25)',
                    animation: 'fadeIn 0.8s ease-out',
                }}>
                    {loading ? (
                        <div style={{
                            textAlign: 'center',
                            padding: '40px 20px',
                        }}>
                            <div className="loading-spinner" style={{
                                width: '50px',
                                height: '50px',
                                border: '5px solid rgba(3, 79, 175, 0.1)',
                                borderRadius: '50%',
                                borderTop: '5px solid #034FAF',
                                animation: 'spin 1s linear infinite',
                                margin: '0 auto 20px',
                            }} />
                            <p style={{
                                fontSize: '16px',
                                fontWeight: '600',
                                color: '#034FAF',
                            }}>Loading transcript files...</p>
                        </div>
                    ) : error ? (
                        <div style={{
                            textAlign: 'center',
                            padding: '40px 20px',
                            color: '#d32f2f',
                        }}>
                            <h3>Error Loading Files</h3>
                            <p>{error}</p>
                            <button
                                onClick={loadTranscriptFiles}
                                style={{
                                    padding: '10px 20px',
                                    background: '#034FAF',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    fontWeight: '600',
                                    marginTop: '20px',
                                }}
                            >
                                Try Again
                            </button>
                        </div>
                    ) : groupedFiles.length === 0 ? (
                        <div style={{
                            textAlign: 'center',
                            padding: '40px 20px',
                            color: '#566573',
                        }}>
                            <div style={{
                                width: '80px',
                                height: '80px',
                                margin: '0 auto 20px',
                                background: 'rgba(3, 79, 175, 0.1)',
                                borderRadius: '50%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                            }}>
                                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="#034FAF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            <h3 style={{
                                fontSize: '20px',
                                fontWeight: '700',
                                color: '#034FAF',
                                margin: '0 0 10px',
                            }}>No Transcript Files Found</h3>
                            <p style={{
                                fontSize: '16px',
                                lineHeight: '1.6',
                                maxWidth: '500px',
                                margin: '0 auto 30px',
                            }}>
                                No transcript files were found in the transcription directory.
                                Upload a recording to generate transcripts.
                            </p>
                            <button
                                onClick={() => navigate('/upload')}
                                style={{
                                    padding: '12px 24px',
                                    background: '#034FAF',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    fontWeight: '600',
                                    transition: 'all 0.3s ease',
                                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                                }}
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.background = '#023e8a';
                                    e.currentTarget.style.transform = 'translateY(-2px)';
                                    e.currentTarget.style.boxShadow = '0 6px 8px rgba(0, 0, 0, 0.15)';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.background = '#034FAF';
                                    e.currentTarget.style.transform = 'translateY(0)';
                                    e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                                }}
                            >
                                Upload a Recording
                            </button>
                        </div>
                    ) : (
                        <table style={{
                            width: '100%',
                            borderCollapse: 'separate',
                            borderSpacing: '0 12px'
                        }}>
                            <thead>
                                <tr>
                                    <th style={{...tableHeaderStyle, width: '30%'}}>FILE NAME</th>
                                    <th style={{...tableHeaderStyle, width: '15%'}}>TYPE</th>
                                    <th style={{...tableHeaderStyle, width: '15%'}}>SIZE</th>
                                    <th style={{...tableHeaderStyle, width: '20%'}}>DATE CREATED</th>
                                    <th style={{...tableHeaderStyle, width: '20%'}}>MODIFIED</th>
                                </tr>
                            </thead>
                            <tbody>
                                {groupedFiles.map((group, groupIndex) => (
                                    <React.Fragment key={groupIndex}>
                                        {group.map((file, fileIndex) => {
                                            // Determine if this is a Tagalog or English transcript
                                            const isTagalog = file.name.includes('_tagalog');
                                            const fileType = isTagalog ? 'Tagalog' : 'English';

                                            // Extract a more readable name
                                            let displayName = file.name;
                                            if (displayName.includes('-')) {
                                                displayName = displayName.split('-').slice(1).join('-'); // Remove timestamp prefix
                                            }
                                            displayName = displayName
                                                .replace('_transcript_tagalog.txt', '')
                                                .replace('_transcript.txt', '')
                                                .replace(/_/g, ' ')
                                                .trim();

                                            // Capitalize first letter of each word
                                            displayName = displayName.split(' ')
                                                .map(word => word.charAt(0).toUpperCase() + word.slice(1))
                                                .join(' ');

                                            return (
                                                <tr
                                                    key={`${groupIndex}-${fileIndex}`}
                                                    onClick={() => handleFileClick({...file, type: fileType})}
                                                    style={{
                                                        cursor: 'pointer',
                                                        transition: 'all 0.3s ease',
                                                        background: 'rgba(255, 255, 255, 0.5)',
                                                        borderRadius: '10px',
                                                        boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
                                                        border: '1px solid rgba(255, 255, 255, 0.3)',
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                                        e.currentTarget.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.1)';
                                                        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.7)';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.currentTarget.style.transform = 'translateY(0)';
                                                        e.currentTarget.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
                                                        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.5)';
                                                    }}
                                                >
                                                    <td style={{
                                                        padding: '15px 20px',
                                                        fontWeight: '600',
                                                        color: '#034FAF',
                                                    }}>
                                                        {displayName}
                                                    </td>
                                                    <td style={{
                                                        padding: '15px 20px',
                                                        color: isTagalog ? '#2e7d32' : '#0277bd',
                                                        fontWeight: '600',
                                                    }}>
                                                        <span style={{
                                                            display: 'inline-block',
                                                            padding: '5px 10px',
                                                            borderRadius: '4px',
                                                            background: isTagalog ? 'rgba(46, 125, 50, 0.1)' : 'rgba(2, 119, 189, 0.1)',
                                                        }}>
                                                            {fileType}
                                                        </span>
                                                    </td>
                                                    <td style={{
                                                        padding: '15px 20px',
                                                        color: '#555',
                                                    }}>
                                                        {formatFileSize(file.size)}
                                                    </td>
                                                    <td style={{
                                                        padding: '15px 20px',
                                                        color: '#555',
                                                    }}>
                                                        {formatDate(file.created || file.birthtime || file.modified)}
                                                    </td>
                                                    <td style={{
                                                        padding: '15px 20px',
                                                        color: '#555',
                                                    }}>
                                                        {formatDate(file.modified)}
                                                    </td>
                                                </tr>
                                            );
                                        })}
                                    </React.Fragment>
                                ))}
                            </tbody>
                        </table>
                    )}
                </div>
            </div>
        </div>
    );
};

export default TranscriptFileList;
