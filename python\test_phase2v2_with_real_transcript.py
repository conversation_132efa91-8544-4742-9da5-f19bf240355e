"""
Test script for Phase2V2 with GPT-2 agenda identification using a real transcript
"""
import os
import sys
import time
import glob
from datetime import timedelta

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def find_transcript_files(directory="transcription", extensions=(".txt",)):
    """
    Find transcript files in the specified directory.
    
    Args:
        directory (str): Directory to search for transcripts
        extensions (tuple): File extensions to include
        
    Returns:
        list: List of transcript file paths
    """
    transcript_files = []
    
    # Check if directory exists
    directory_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), directory)
    if not os.path.exists(directory_path):
        print(f"Directory '{directory_path}' does not exist.")
        return transcript_files
    
    # Find all files with the specified extensions
    for ext in extensions:
        pattern = os.path.join(directory_path, f"*{ext}")
        transcript_files.extend(glob.glob(pattern))
    
    # Sort by modification time (newest first)
    transcript_files.sort(key=os.path.getmtime, reverse=True)
    
    return transcript_files

def load_transcript(file_path):
    """
    Load a transcript from a file.
    
    Args:
        file_path (str): Path to the transcript file
        
    Returns:
        str: Transcript content
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error loading transcript from {file_path}: {e}")
        return None

def main():
    """Test Phase2V2 with GPT-2 agenda identification using a real transcript."""
    start_time = time.time()
    
    print("Testing Phase2V2 with GPT-2 agenda identification using a real transcript...")
    
    # Add the specific directory to the path
    pipeline_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Pipeline Phase2')
    sys.path.append(pipeline_dir)
    
    # Import the necessary functions
    try:
        from Phase2V2 import classify_agenda_enhanced, process_meeting_transcript_enhanced
        print("Successfully imported functions from Phase2V2")
    except ImportError as e:
        print(f"Error importing functions: {e}")
        print("Available modules in sys.path:")
        for path in sys.path:
            print(f"  - {path}")
            if os.path.exists(path):
                print(f"    Contents: {os.listdir(path)}")
        return
    
    # Find transcript files
    transcript_files = find_transcript_files()
    
    if not transcript_files:
        print("No transcript files found.")
        return
    
    # Display found transcripts
    print(f"\nFound {len(transcript_files)} transcript files:")
    for i, file_path in enumerate(transcript_files[:10], 1):  # Show only first 10
        file_size = os.path.getsize(file_path) / 1024  # KB
        print(f"{i}. {os.path.basename(file_path)} ({file_size:.1f} KB)")
    
    if len(transcript_files) > 10:
        print(f"...and {len(transcript_files) - 10} more")
    
    # Ask which transcript to process
    while True:
        try:
            choice = input("\nEnter the number of the transcript to process (or 'q' to quit): ")
            
            if choice.lower() == 'q':
                break
            
            index = int(choice) - 1
            if 0 <= index < len(transcript_files):
                file_path = transcript_files[index]
                transcript = load_transcript(file_path)
                
                if transcript:
                    print(f"\nProcessing transcript: {os.path.basename(file_path)}")
                    print(f"Transcript length: {len(transcript)} characters")
                    
                    # Test agenda classification
                    print("\nTesting agenda classification...")
                    try:
                        category = classify_agenda_enhanced(transcript)
                        print(f"Classified as: {category}")
                        print("Classification successful!")
                    except Exception as e:
                        print(f"Error during classification: {e}")
                        import traceback
                        print(traceback.format_exc())
                        print("Classification failed!")
                    
                    # Ask if user wants to process the full transcript
                    process_full = input("\nDo you want to process the full transcript? (y/n): ")
                    if process_full.lower() == 'y':
                        print("\nProcessing full transcript...")
                        try:
                            minutes = process_meeting_transcript_enhanced(transcript)
                            
                            # Save the minutes to a file
                            output_file = file_path.replace('.txt', '_minutes.md')
                            with open(output_file, 'w', encoding='utf-8') as f:
                                f.write(minutes)
                            
                            print(f"Minutes saved to: {output_file}")
                        except Exception as e:
                            print(f"Error processing transcript: {e}")
                            import traceback
                            print(traceback.format_exc())
                            print("Processing failed!")
                else:
                    print(f"Could not load transcript from {file_path}")
            else:
                print(f"Please enter a number between 1 and {len(transcript_files)}")
        
        except ValueError:
            print("Please enter a valid number or 'q' to quit")
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            break
    
    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nTotal processing time: {format_time(processing_time)}")

if __name__ == "__main__":
    main()
