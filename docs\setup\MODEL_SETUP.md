# BART Model Setup Guide

## 🎯 Overview

The Dean's Office AI-Powered Meeting Assistant requires the BART (Bidirectional and Auto-Regressive Transformers) model for generating meeting minutes from transcripts. Due to GitHub's file size limitations (100MB), the large model file (1.5GB) cannot be stored directly in the repository.

## 📋 Model Requirements

- **Model**: Facebook BART Large CNN
- **Size**: ~1.5GB
- **Purpose**: Meeting minutes generation and summarization
- **Location**: `core/pipelines/Pipeline Phase2/meeting-summary-bart-large/`

## 🚀 Setup Options

### Option 1: Automatic Download (Recommended)

Use the built-in model downloader utility:

```bash
# Navigate to project directory
cd meeting-assistantv2

# Run the model downloader
python core/utils/model_downloader.py ensure
```

This will automatically:
- Check if the model exists
- Download if missing
- Verify the installation

### Option 2: Manual Download

If automatic download fails, manually download the model:

```python
from transformers import BartForConditionalGeneration, BartTokenizer

# Create the model directory
import os
os.makedirs("core/pipelines/Pipeline Phase2/meeting-summary-bart-large", exist_ok=True)

# Download and save the model
model_name = "facebook/bart-large-cnn"
tokenizer = BartTokenizer.from_pretrained(model_name)
model = BartForConditionalGeneration.from_pretrained(model_name)

# Save to the correct location
save_path = "core/pipelines/Pipeline Phase2/meeting-summary-bart-large"
tokenizer.save_pretrained(save_path)
model.save_pretrained(save_path)
```

### Option 3: Copy from Existing Installation

If you already have the model files:

1. Locate your existing model files
2. Copy them to: `core/pipelines/Pipeline Phase2/meeting-summary-bart-large/`
3. Ensure the following files are present:
   - `config.json`
   - `model.safetensors` (or `pytorch_model.bin`)
   - `tokenizer.json`
   - `vocab.json`
   - `merges.txt`

## 🔍 Verification

To verify the model is properly installed:

```bash
python core/utils/model_downloader.py verify
```

You should see: ✅ BART model verification successful!

## 🚨 Troubleshooting

### Common Issues:

1. **Internet Connection**: Model download requires stable internet
2. **Disk Space**: Ensure you have at least 2GB free space
3. **Permissions**: Make sure you have write permissions in the project directory
4. **Python Dependencies**: Ensure transformers library is installed:
   ```bash
   pip install transformers torch
   ```

### Error Messages:

- **"Model not found"**: Run the downloader utility
- **"Verification failed"**: Delete the model directory and re-download
- **"Permission denied"**: Check file/folder permissions

## 📚 Academic Context

This model setup is part of the undergraduate thesis project:

- **Project**: Dean's Office AI-Powered Meeting Assistant
- **Institution**: Laguna State Polytechnic University Santa Cruz Campus
- **Team**: John Richard Bercades, Baron Denver Niegos, Nicko Venancio
- **Supervisor**: Dean Mia V. Villarica, D.I.T.

## 🔒 Important Notes

1. **Repository Size**: The model is excluded from Git to keep repository manageable
2. **First Run**: Initial setup may take 10-15 minutes for model download
3. **Offline Use**: Once downloaded, the system works offline
4. **Updates**: Model updates should be done through the utility script

## 📞 Support

If you encounter issues with model setup:

1. Check the troubleshooting section above
2. Verify your internet connection
3. Ensure all dependencies are installed
4. Contact the development team for thesis-related support

---

*This setup guide ensures the AI-powered meeting assistant has all required components while maintaining a clean, manageable repository structure.*
