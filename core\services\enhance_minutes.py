"""
Script to enhance meeting minutes using GPT-2
"""
import os
import sys
import time
import argparse
from datetime import timedelta

# Add the Pipeline Phase2 directory to the path
sys.path.append('Pipeline Phase2')

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    """Enhance meeting minutes using GPT-2."""
    parser = argparse.ArgumentParser(description='Enhance meeting minutes using GPT-2')
    parser.add_argument('input_file', help='Path to the input minutes file')
    parser.add_argument('--output_file', help='Path to the output enhanced minutes file (default: input_file with _enhanced suffix)')
    parser.add_argument('--force_cpu', action='store_true', help='Force CPU usage even if GPU is available')
    args = parser.parse_args()
    
    start_time = time.time()
    
    print(f"Enhancing minutes file: {args.input_file}")
    
    # Import the minutes enhancement function
    try:
        from gpt2_minutes_enhancer import process_minutes_for_enhancement
        print("Successfully imported GPT-2 minutes enhancement")
    except ImportError as e:
        print(f"Error importing GPT-2 minutes enhancement: {e}")
        return
    
    # Override GPU usage if requested
    if args.force_cpu:
        try:
            import os
            os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
            print("Forcing CPU usage as requested")
        except:
            pass
    
    # Read the input file
    try:
        with open(args.input_file, 'r', encoding='utf-8') as f:
            minutes_text = f.read()
        
        print(f"Read {len(minutes_text)} characters from {args.input_file}")
    except Exception as e:
        print(f"Error reading input file: {e}")
        return
    
    # Process the minutes
    try:
        enhanced_minutes = process_minutes_for_enhancement(minutes_text)
        
        # Determine output file path
        if args.output_file:
            output_path = args.output_file
        else:
            base, ext = os.path.splitext(args.input_file)
            output_path = f"{base}_enhanced{ext}"
        
        # Save the enhanced minutes
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_minutes)
        
        print(f"Enhanced minutes saved to: {output_path}")
        
        # Print processing time
        processing_time = time.time() - start_time
        print(f"Processing completed in {format_time(processing_time)}")
        
    except Exception as e:
        print(f"Error enhancing minutes: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
