.sidebar {
  position: fixed; /* Ensures sidebar stays in place */
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  background: rgba(240, 245, 255, 0.95);
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  backdrop-filter: blur(10px);
  padding: 15px; /* Slightly increased padding */
  height: 100vh;
  width: 320px;
  box-shadow: 4px 0 30px rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(3, 79, 175, 0.1);
  font-family: 'Montserrat', sans-serif;
  z-index: 1000;
}

.logo-section {
  text-align: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(3, 79, 175, 0.1);
}

.big-logo {
  width: 120px;
  transition: transform 0.3s ease;
}

.big-logo:hover {
  transform: scale(1.05);
}

.user-info {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, rgba(3, 79, 175, 0.15) 0%, rgba(87, 215, 226, 0.15) 100%);
  padding: 15px;
  border-radius: 12px;
  margin: 15px 0;
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.user-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid #034FAF;
  box-shadow: 0 2px 10px rgba(3, 79, 175, 0.2);
  transition: all 0.3s ease;
}

.user-info:hover .avatar {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(3, 79, 175, 0.3);
}

.user-details {
  margin-left: 15px;
}

.user-name {
  font-weight: 700;
  color: #034FAF;
  margin: 0;
  font-size: 0.95rem;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.7);
  cursor: default;
}

.user-email {
  font-size: 0.8rem;
  color: #333;
  margin: 3px 0 0 0;
  cursor: default;
}

.nav-menu {
  flex: 0.7;
  overflow: hidden;
}

.nav-menu ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin: 8px 0;
  border-radius: 10px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 600;
  border: 1px solid transparent;
  width: calc(100% - 10px);
}

.menu-item:hover {
  background: rgba(3, 79, 175, 0.1);
  color: #034FAF;
  transform: none;
  border: none;
  box-shadow: none;
}

.menu-item.active {
  background: linear-gradient(90deg, #034FAF, #57D7E2);
  color: white;
  box-shadow: 0 4px 15px rgba(3, 79, 175, 0.2);
  border: none;
  border-radius: 10px;
  transform: none;
}

.menu-icon {
  width: 22px;
  height: 22px;
  margin-right: 12px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.menu-item:hover .menu-icon,
.footer-item:hover .menu-icon {
  transform: scale(1.1);
}

.sidebar-footer {
  flex: 0.3;
  border-top: 1px solid rgba(255, 255, 255, 0.25);
  padding-top: 15px;
  margin-top: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.footer-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 10px;
  margin: 8px 0;
  font-weight: 600;
  border: 1px solid transparent;
  width: calc(100% - 10px);
}

.footer-item:hover {
  background: rgba(3, 79, 175, 0.1);
  color: #034FAF;
  transform: none;
  border: none;
  box-shadow: none;
}

.footer-item.active {
  background: linear-gradient(90deg, #034FAF, #57D7E2);
  color: white;
  box-shadow: 0 4px 15px rgba(3, 79, 175, 0.2);
  border: none;
  border-radius: 10px;
  transform: none;
}


