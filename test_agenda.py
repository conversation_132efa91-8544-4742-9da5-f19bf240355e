"""
Test GPT-2 agenda identification with a real transcript
"""
import os
import sys
import time
from datetime import timedelta

# Add the python directory to the path
sys.path.append('python')
# Add the Pipeline Phase2 directory to the path
sys.path.append(os.path.join('python', 'Pipeline Phase2'))

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    """Test GPT-2 agenda identification with a real transcript."""
    # Specify the transcript file
    transcript_file = os.path.join('python', 'transcription', '1745915695705-April Faculty Meeting_transcript.txt')
    
    # Check if the file exists
    if not os.path.isfile(transcript_file):
        print(f"File not found: {transcript_file}")
        return
    
    # Load the transcript
    try:
        with open(transcript_file, 'r', encoding='utf-8') as f:
            transcript = f.read()
    except Exception as e:
        print(f"Error loading transcript: {e}")
        return
    
    print(f"Loaded transcript from {transcript_file}")
    print(f"Transcript length: {len(transcript)} characters")
    print(f"First 200 characters: {transcript[:200]}...")
    
    # Import the GPT-2 agenda identification function
    try:
        from gpt2_agenda import process_transcript_for_agenda
        print("Successfully imported GPT-2 agenda identification")
    except ImportError as e:
        print(f"Error importing GPT-2 agenda identification: {e}")
        return
    
    # Process the transcript
    start_time = time.time()
    print("\nProcessing transcript with GPT-2 agenda identification...")
    
    try:
        results = process_transcript_for_agenda(transcript)
        
        if results:
            print(f"\nIdentified {len(results)} agenda items:")
            print("-" * 50)
            
            # Save results to a file
            output_file = os.path.join('python', 'transcription', '1745915695705-April Faculty Meeting_agenda.txt')
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"Agenda items identified in April Faculty Meeting transcript:\n")
                f.write("-" * 50 + "\n\n")
                
                for i, (segment, category) in enumerate(results, 1):
                    # Print to console (truncated)
                    print(f"Item {i}: {category}")
                    print(f"Segment: {segment[:100]}..." if len(segment) > 100 else f"Segment: {segment}")
                    print("-" * 50)
                    
                    # Write to file (full content)
                    f.write(f"Item {i}: {category}\n")
                    f.write(f"Segment:\n{segment}\n")
                    f.write("-" * 50 + "\n\n")
            
            print(f"Results saved to: {output_file}")
        else:
            print("No agenda items identified or processing failed.")
    
    except Exception as e:
        print(f"Error processing transcript: {e}")
        import traceback
        print(traceback.format_exc())
    
    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nProcessing completed in {format_time(processing_time)}")

if __name__ == "__main__":
    main()
