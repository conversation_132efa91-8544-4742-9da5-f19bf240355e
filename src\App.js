import React from "react";
import { BrowserRouter as Router, Route, Routes, Navigate } from "react-router-dom";

// Authentication Components
import Login from "./components/Login";
import SignUp from "./components/SignUp";
import SplashScreen from "./components/SplashScreen";
import Home from "./components/Home";
import Sidebar from "./components/Sidebar";

// Recording Flow Components
import UploadRecorded from "./components/UploadRecorded";
import RecordSaved from "./components/RecordSaved";

// Conversation Components
import ConversationList from "./components/ConversationList";
import Conversation from "./components/Conversation";
import TranscriptFileList from "./components/TranscriptFileList";


// Minutes Components
import MinutesList from "./components/MinutesList";
import Minutes from "./components/Minutes";

// User Settings Component
import Settings from "./components/Settings";
import ProfilePictureTest from "./components/ProfilePictureTest";


// Debug Component
import DebugStorage from "./components/DebugStorage";

// Add Platform Context
const PlatformContext = React.createContext();

const styles = {
  pageContainer: {
    width: '100vw',
    height: '100vh',
    margin: 0,
    padding: 0,
    overflow: 'hidden',
    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
    display: 'flex',
    position: 'relative',
    scrollbarWidth: 'none',
    msOverflowStyle: 'none'
  }
};

const ProtectedRoute = ({ children }) => {
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

const PageLayout = ({ children }) => (
  <div style={styles.pageContainer}>
    <Sidebar />
    <div className="page-content" style={{
      marginLeft: '320px',
      paddingLeft: '20px', /* Added padding to create more space from sidebar */
      paddingBottom: '40px', /* Added bottom padding for more space from the bottom */
      flex: 1,
      height: '100%',
      overflow: 'auto',
      position: 'relative',
      scrollbarWidth: 'none',
      msOverflowStyle: 'none'
    }}>
      {children}
    </div>
  </div>
);

const App = () => {
  // Detect platform
  const [platform] = React.useState(() => {
    if (window?.electronAPI?.isElectron) {
      return 'electron';
    }
    return 'web';
  });

  return (
    <PlatformContext.Provider value={platform}>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<SplashScreen />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<SignUp />} />

          {/* Protected Routes */}
          <Route path="/home" element={
            <ProtectedRoute>
              <PageLayout>
                <Home />
              </PageLayout>
            </ProtectedRoute>
          } />

          {/* Recording Flow Routes */}
          <Route path="/upload-recorded" element={
            <ProtectedRoute>
              <PageLayout>
                <UploadRecorded />
              </PageLayout>
            </ProtectedRoute>
          } />

          <Route path="/record-saved" element={
            <ProtectedRoute>
              <PageLayout>
                <RecordSaved />
              </PageLayout>
            </ProtectedRoute>
          } />

          {/* Transcription Routes */}
          <Route path="/transcriptions" element={
            <ProtectedRoute>
              <PageLayout>
                <ConversationList />
              </PageLayout>
            </ProtectedRoute>
          } />

          <Route path="/transcription/:id" element={
            <ProtectedRoute>
              <PageLayout>
                <Conversation />
              </PageLayout>
            </ProtectedRoute>
          } />

          <Route path="/all-transcripts" element={
            <ProtectedRoute>
              <PageLayout>
                <TranscriptFileList />
              </PageLayout>
            </ProtectedRoute>
          } />



          {/* Minutes Routes */}
          <Route path="/minutes" element={
            <ProtectedRoute>
              <PageLayout>
                <MinutesList />
              </PageLayout>
            </ProtectedRoute>
          } />

          <Route path="/minutes/:id" element={
            <ProtectedRoute>
              <PageLayout>
                <Minutes />
              </PageLayout>
            </ProtectedRoute>
          } />

          {/* Settings Route */}
          <Route path="/settings" element={
            <ProtectedRoute>
              <PageLayout>
                <Settings />
              </PageLayout>
            </ProtectedRoute>
          } />

          {/* Test Routes - Remove in production */}
          <Route path="/profile-test" element={
            <ProtectedRoute>
              <PageLayout>
                <ProfilePictureTest />
              </PageLayout>
            </ProtectedRoute>
          } />



          {/* Debug Route */}
          <Route path="/debug-storage" element={
            <ProtectedRoute>
              <DebugStorage />
            </ProtectedRoute>
          } />

          {/* Catch-all Route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Router>
    </PlatformContext.Provider>
  );
};

// Export both the component and the context
export { PlatformContext };
export default App;
