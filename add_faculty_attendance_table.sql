-- Create faculty_members table if it doesn't exist
CREATE TABLE IF NOT EXISTS faculty_members (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  role VARCHAR(50) DEFAULT 'Faculty',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create faculty_attendance table if it doesn't exist
CREATE TABLE IF NOT EXISTS faculty_attendance (
  id INT AUTO_INCREMENT PRIMARY KEY,
  minute_id INT NOT NULL,
  faculty_id INT NOT NULL,
  status ENUM('present', 'absent') DEFAULT 'present',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (minute_id) REFERENCES minutes(id) ON DELETE CASCADE,
  FOREIGN KEY (faculty_id) REFERENCES faculty_members(id) ON DELETE CASCADE
);

-- Insert default faculty members if they don't exist
INSERT IGNORE INTO faculty_members (name, role) VALUES
('<PERSON>', 'Dean'),
('<PERSON>', '<PERSON>'),
('<PERSON><PERSON>', 'Faculty'),
('<PERSON><PERSON>', 'Faculty'),
('<PERSON>ondo', 'Faculty'),
('Joy Mell J', 'Faculty'),
('Maria Laureen Miranda', 'Faculty'),
('<PERSON> P. Bernardino', 'Faculty'),
('<PERSON> <PERSON> <PERSON><PERSON><PERSON>', 'Faculty'),
('Ra<PERSON><PERSON> <PERSON>. <PERSON>', 'Faculty'),
('<PERSON><PERSON> <PERSON>', 'Faculty'),
('<PERSON>n <PERSON>. <PERSON><PERSON>', 'Faculty'),
('<PERSON><PERSON>n John <PERSON> L. <PERSON><PERSON><PERSON>', 'Faculty'),
('Romel P. Serrano', 'Faculty');
