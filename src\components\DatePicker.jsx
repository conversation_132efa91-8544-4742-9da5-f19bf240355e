import React, { useState, useEffect, useRef } from 'react';

const DatePicker = ({ selectedDate, onChange, label = "Meeting Date" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(selectedDate ? new Date(selectedDate) : new Date());
  const [hoveredDate, setHoveredDate] = useState(null);
  const calendarRef = useRef(null);

  // Close calendar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get days in month
  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  // Get day of week for first day of month (0 = Sunday, 6 = Saturday)
  const getFirstDayOfMonth = (year, month) => {
    return new Date(year, month, 1).getDay();
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    
    const days = [];
    
    // Add empty cells for days before the first day of month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push({ day: null, isCurrentMonth: false });
    }
    
    // Add days of current month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({ day, isCurrentMonth: true });
    }
    
    return days;
  };

  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Handle date selection
  const handleDateSelect = (day) => {
    if (!day) return;
    
    const newDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    onChange(newDate);
    setIsOpen(false);
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  // Check if a date is the selected date
  const isSelectedDate = (day) => {
    if (!selectedDate || !day) return false;
    
    const date = new Date(selectedDate);
    return date.getDate() === day && 
           date.getMonth() === currentMonth.getMonth() && 
           date.getFullYear() === currentMonth.getFullYear();
  };

  // Check if a date is today
  const isToday = (day) => {
    if (!day) return false;
    
    const today = new Date();
    return today.getDate() === day && 
           today.getMonth() === currentMonth.getMonth() && 
           today.getFullYear() === currentMonth.getFullYear();
  };

  return (
    <div style={{ position: 'relative', width: '100%' }}>
      <label style={{
        display: 'block',
        marginBottom: '8px',
        fontSize: '16px',
        fontWeight: '600',
        color: '#034FAF',
      }}>
        {label}
      </label>
      
      <div 
        onClick={() => setIsOpen(!isOpen)}
        style={{
          padding: '12px 15px',
          borderRadius: '8px',
          border: '1px solid rgba(3, 79, 175, 0.3)',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          cursor: 'pointer',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          transition: 'all 0.3s ease',
          boxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)',
          backdropFilter: 'blur(5px)',
          WebkitBackdropFilter: 'blur(5px)',
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
          e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
          e.currentTarget.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.05)';
        }}
      >
        <span>{selectedDate ? formatDate(selectedDate) : 'Select a date'}</span>
        <span style={{ color: '#034FAF' }}>📅</span>
      </div>
      
      {isOpen && (
        <div 
          ref={calendarRef}
          style={{
            position: 'absolute',
            top: 'calc(100% + 5px)',
            left: 0,
            width: '300px',
            backgroundColor: 'white',
            borderRadius: '12px',
            boxShadow: '0 5px 20px rgba(0, 0, 0, 0.15)',
            zIndex: 1000,
            padding: '15px',
            animation: 'fadeIn 0.2s ease-out',
            border: '1px solid rgba(3, 79, 175, 0.2)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
          }}
        >
          {/* Calendar Header */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px',
          }}>
            <button 
              onClick={(e) => {
                e.stopPropagation();
                goToPreviousMonth();
              }}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                color: '#034FAF',
                padding: '5px 10px',
                borderRadius: '5px',
                transition: 'background-color 0.2s',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(3, 79, 175, 0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              ◀
            </button>
            
            <div style={{
              fontWeight: '600',
              fontSize: '16px',
              color: '#034FAF',
            }}>
              {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </div>
            
            <button 
              onClick={(e) => {
                e.stopPropagation();
                goToNextMonth();
              }}
              style={{
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '18px',
                color: '#034FAF',
                padding: '5px 10px',
                borderRadius: '5px',
                transition: 'background-color 0.2s',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(3, 79, 175, 0.1)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              ▶
            </button>
          </div>
          
          {/* Weekday Headers */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            gap: '5px',
            marginBottom: '10px',
          }}>
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} style={{
                textAlign: 'center',
                fontSize: '14px',
                fontWeight: '600',
                color: '#666',
                padding: '5px 0',
              }}>
                {day}
              </div>
            ))}
          </div>
          
          {/* Calendar Days */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(7, 1fr)',
            gap: '5px',
          }}>
            {generateCalendarDays().map((item, index) => (
              <div 
                key={index}
                onClick={() => item.isCurrentMonth && handleDateSelect(item.day)}
                onMouseEnter={() => item.isCurrentMonth && setHoveredDate(item.day)}
                onMouseLeave={() => setHoveredDate(null)}
                style={{
                  textAlign: 'center',
                  padding: '8px 0',
                  borderRadius: '8px',
                  cursor: item.isCurrentMonth ? 'pointer' : 'default',
                  backgroundColor: isSelectedDate(item.day) 
                    ? '#034FAF' 
                    : hoveredDate === item.day && item.isCurrentMonth
                      ? 'rgba(3, 79, 175, 0.1)'
                      : isToday(item.day)
                        ? 'rgba(3, 79, 175, 0.05)'
                        : 'transparent',
                  color: isSelectedDate(item.day) 
                    ? 'white' 
                    : isToday(item.day) && !isSelectedDate(item.day)
                      ? '#034FAF'
                      : item.isCurrentMonth 
                        ? '#333' 
                        : '#ccc',
                  fontWeight: isToday(item.day) || isSelectedDate(item.day) ? '600' : '400',
                  border: isToday(item.day) && !isSelectedDate(item.day) ? '1px solid #034FAF' : 'none',
                  transition: 'all 0.2s ease',
                }}
              >
                {item.day}
              </div>
            ))}
          </div>
          
          {/* Today Button */}
          <div style={{
            marginTop: '15px',
            display: 'flex',
            justifyContent: 'center',
          }}>
            <button 
              onClick={(e) => {
                e.stopPropagation();
                const today = new Date();
                setCurrentMonth(today);
                onChange(today);
                setIsOpen(false);
              }}
              style={{
                backgroundColor: 'rgba(3, 79, 175, 0.1)',
                color: '#034FAF',
                border: 'none',
                padding: '8px 15px',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: '500',
                transition: 'all 0.2s ease',
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(3, 79, 175, 0.2)';
              }}
              onMouseOut={(e) => {
                e.currentTarget.style.backgroundColor = 'rgba(3, 79, 175, 0.1)';
              }}
            >
              Today
            </button>
          </div>
        </div>
      )}
      
      <style>
        {`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `}
      </style>
    </div>
  );
};

export default DatePicker;
