"""
Progress reporting utilities for the meeting assistant pipeline.
This module provides functions for reporting progress to the frontend.
"""

import sys
import json
import time
import threading
import os
from datetime import datetime, timedelta

def send_progress(stage, progress, details=None, estimated_time=None, current_step=None, total_steps=None, iteration=None, total_iterations=None):
    """
    Send progress updates to stderr in JSON format for Node.js to parse.

    Args:
        stage (str): The current processing stage (e.g., "Transcribing", "Generating Minutes")
        progress (int): Progress percentage (0-100)
        details (str, optional): Detailed description of the current step
        estimated_time (int, optional): Estimated time remaining in seconds
        current_step (int, optional): Current step number
        total_steps (int, optional): Total number of steps
        iteration (int, optional): Current iteration number (0-based)
        total_iterations (int, optional): Total expected iterations
    """
    # Track last progress value per stage to prevent backward movement
    if not hasattr(send_progress, '_last_progress_values'):
        send_progress._last_progress_values = {}

    # Track iteration information per stage
    if not hasattr(send_progress, '_iteration_info'):
        send_progress._iteration_info = {}

    # If iteration info is provided, store it
    if iteration is not None and total_iterations is not None:
        send_progress._iteration_info[stage] = (iteration, total_iterations)

    # Calculate overall progress that never goes backward
    original_progress = progress

    # If we have iteration info (either from this call or previous calls)
    if stage in send_progress._iteration_info:
        current_iter, total_iters = send_progress._iteration_info[stage]

        # Calculate overall progress based on iteration
        # First 10%: Initial setup
        # Next 80%: Distributed across iterations (each iteration gets equal share)
        # Final 10%: Final processing
        iter_progress = min(10 + (80 * (current_iter + 1) / total_iters), 90)

        # For the final iteration, allow progress to reach 100%
        if current_iter >= total_iters - 1 and original_progress >= 95:
            iter_progress = min(iter_progress + (original_progress - 95) * 2, 100)

        # Use the iteration-based progress
        progress = round(iter_progress)

        # Update details to include iteration info if not already included
        if details and not details.startswith(f"Iteration {current_iter+1}"):
            details = f"Iteration {current_iter+1}/{total_iters}: {details}"

    # Ensure progress never decreases for a given stage
    if stage in send_progress._last_progress_values:
        progress = max(progress, send_progress._last_progress_values[stage])

    # Update the last progress value for this stage
    send_progress._last_progress_values[stage] = progress

    # Create progress data object
    progress_data = {
        "type": "progress",
        "stage": stage,
        "progress": progress
    }

    # Add details if provided
    if details:
        progress_data["details"] = details

    # Add estimated time if provided
    if estimated_time is not None:
        progress_data["estimated_time"] = estimated_time
        eta_str = str(timedelta(seconds=int(estimated_time)))
        if details:
            details += f" (ETA: {eta_str})"
        else:
            details = f"ETA: {eta_str}"

    # Add step information if provided
    if current_step is not None and total_steps is not None:
        progress_data["current_step"] = current_step
        progress_data["total_steps"] = total_steps
        step_info = f"Step {current_step}/{total_steps}"
        if details:
            details = f"{step_info} - {details}"
        else:
            details = step_info

    # Add iteration information if available
    if stage in send_progress._iteration_info:
        current_iter, total_iters = send_progress._iteration_info[stage]
        progress_data["iteration"] = current_iter + 1  # Make 1-based for display
        progress_data["total_iterations"] = total_iters
        progress_data["original_progress"] = original_progress  # Include original progress for debugging

    # Print to stdout for logging
    if details:
        print(f"{stage}: {progress}% - {details}", file=sys.stdout, flush=True)
    else:
        print(f"{stage}: {progress}%", file=sys.stdout, flush=True)

    # Send JSON data to stderr for Node.js to parse
    sys.stderr.write(json.dumps(progress_data) + "\n")
    sys.stderr.flush()

    # Additional flush to ensure output reaches the frontend
    sys.stdout.flush()

class ProgressReporter:
    """
    Enhanced progress reporter with time-based updates and estimated time remaining.

    This class provides a more responsive progress reporting system that:
    1. Sends regular time-based updates for long-running operations
    2. Calculates and displays estimated time remaining
    3. Shows processing stage indicators
    """

    def __init__(self, stage, total_steps=4, update_interval=3.0):
        """
        Initialize the progress reporter.

        Args:
            stage (str): The processing stage (e.g., "Transcribing", "Generating Minutes")
            total_steps (int): Total number of processing steps
            update_interval (float): How often to send time-based updates (in seconds)
        """
        self.stage = stage
        self.current_step = 0
        self.total_steps = total_steps
        self.update_interval = update_interval
        self.start_time = time.time()
        self.step_start_time = time.time()
        self.last_update_time = 0
        self.last_progress = 0
        self.step_names = []
        self.running = False
        self._thread = None
        self.file_size_mb = None
        self.audio_duration = None

        # Set step names based on typical processing pipeline
        if stage == "Transcribing":
            self.step_names = [
                "Initializing models",
                "Voice activity detection",
                "Speaker identification",
                "Speech recognition"
            ]
        elif stage == "Generating Minutes":
            self.step_names = [
                "Analyzing transcript",
                "Identifying key points",
                "Generating summary",
                "Formatting minutes"
            ]

    def set_file_info(self, file_path=None, audio_duration=None):
        """
        Set file information for better time estimation.

        Args:
            file_path (str, optional): Path to the audio/video file
            audio_duration (float, optional): Duration of the audio in seconds
        """
        if file_path and os.path.exists(file_path):
            self.file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
            print(f"File size: {self.file_size_mb:.2f} MB")

        if audio_duration:
            self.audio_duration = audio_duration
            print(f"Audio duration: {audio_duration:.2f} seconds ({audio_duration/60:.2f} minutes)")

    def start(self):
        """Start the progress reporter with background updates."""
        self.running = True
        self.start_time = time.time()
        self.step_start_time = time.time()
        self._thread = threading.Thread(target=self._update_progress)
        self._thread.daemon = True
        self._thread.start()

    def stop(self):
        """Stop the progress reporter."""
        self.running = False
        if self._thread:
            self._thread.join(timeout=1.0)

    def next_step(self, step_name=None):
        """
        Move to the next processing step.

        Args:
            step_name (str, optional): Custom name for this step
        """
        self.current_step += 1
        self.step_start_time = time.time()

        if step_name and self.current_step <= len(self.step_names):
            self.step_names[self.current_step-1] = step_name

        # Calculate base progress based on step
        base_progress = ((self.current_step - 1) / self.total_steps) * 100

        # Get step name
        if self.current_step <= len(self.step_names):
            current_step_name = self.step_names[self.current_step-1]
        else:
            current_step_name = f"Step {self.current_step}"

        # Send progress update
        self.update(base_progress, f"Starting: {current_step_name}")

    def update(self, progress, details=None, force=False):
        """
        Update progress with the given percentage and details.

        Args:
            progress (int): Progress percentage (0-100)
            details (str, optional): Detailed description of the current step
            force (bool): Force sending update even if time interval hasn't elapsed
        """
        current_time = time.time()

        # Ensure progress never goes backward
        progress = max(progress, self.last_progress)

        # Only send updates if forced, progress has increased, or enough time has passed
        if force or progress > self.last_progress or (current_time - self.last_update_time) >= self.update_interval:
            self.last_update_time = current_time
            self.last_progress = progress

            # Calculate estimated time remaining
            elapsed = current_time - self.start_time
            if progress > 0:
                estimated_total = elapsed / (progress / 100)
                estimated_remaining = estimated_total - elapsed
            else:
                estimated_remaining = self._estimate_time_from_file_info()

            # Send progress update with all available information
            send_progress(
                self.stage,
                int(progress),
                details,
                estimated_remaining,
                self.current_step,
                self.total_steps
            )

    def _update_progress(self):
        """Background thread that sends periodic progress updates."""
        while self.running:
            # Calculate progress within current step
            elapsed_in_step = time.time() - self.step_start_time
            step_size = 100 / self.total_steps

            # Estimate progress within step based on elapsed time
            # Use a logarithmic function to show faster initial progress that slows down
            if self.current_step > 0:
                # Base progress from previous steps
                base_progress = ((self.current_step - 1) / self.total_steps) * 100

                # Progress within current step (max 90% of step to leave room for completion)
                if elapsed_in_step < 1:
                    step_progress = 0
                else:
                    # Logarithmic progress that approaches 90% of step size
                    step_progress = min(0.9 * step_size, (step_size * 0.3 * (1 + (1/3) * (elapsed_in_step))))

                # Calculate total progress
                calculated_progress = base_progress + step_progress

                # Ensure progress never decreases
                progress = max(calculated_progress, self.last_progress)

                # Get current step name
                if self.current_step <= len(self.step_names):
                    current_step_name = self.step_names[self.current_step-1]
                else:
                    current_step_name = f"Step {self.current_step}"

                # Only update if progress has increased significantly
                if progress > self.last_progress + 0.5:
                    self.update(
                        progress,
                        f"In progress: {current_step_name} ({elapsed_in_step:.1f}s elapsed)"
                    )

            # Sleep before next update
            time.sleep(self.update_interval)

    def _estimate_time_from_file_info(self):
        """Estimate processing time based on file size or audio duration."""
        if self.audio_duration:
            # Based on benchmarks: processing takes ~0.2x real-time + 5 min overhead
            return (self.audio_duration * 0.2) + 300
        elif self.file_size_mb:
            # Rough estimate based on file size: ~10 seconds per MB + 5 min overhead
            return (self.file_size_mb * 10) + 300
        else:
            # Default estimate if no file info available
            return 600  # 10 minutes
