const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

const platform = {
  isServerAvailable: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/test`, {
        credentials: 'include'
      });
      return response.ok;
    } catch (error) {
      console.error('Server availability check failed:', error);
      return false;
    }
  },

  processAudio: async (file, callbacks = {}) => {
    const { onProgress, onStage, metadata = {} } = callbacks;
    let eventSource = null;

    try {
      // Validate file
      if (!file) {
        throw new Error('No file provided');
      }

      // Validate file size (5GB limit)
      if (file.size > 5 * 1024 * 1024 * 1024) {
        throw new Error('File size exceeds 5GB limit');
      }

      // Report file analysis
      if (onStage) onStage('Analyzing file', 'Checking file format and size');
      if (onProgress) onProgress(0);

      // Get file extension
      const fileExtension = file.name.split('.').pop().toLowerCase();
      const isVideo = ['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv', 'wmv'].includes(fileExtension);
      const isTextFile = ['txt', 'md'].includes(fileExtension) || metadata.isTextFile;

      // Report file type detection
      if (onStage) {
        if (isTextFile) {
          onStage('Converting', `Text file detected (${fileExtension.toUpperCase()}) - will process as transcript`);
        } else if (isVideo) {
          onStage('Converting', `Video file detected (${fileExtension.toUpperCase()}) - will extract audio`);
        } else {
          onStage('Converting', `Audio file detected (${fileExtension.toUpperCase()})`);
        }
      }
      if (onProgress) onProgress(5);

      const formData = new FormData();
      formData.append('file', file);

      // Add metadata if provided
      if (metadata.userId) formData.append('userId', metadata.userId);
      if (metadata.title) formData.append('title', metadata.title);
      if (metadata.description) formData.append('description', metadata.description);

      // Add text file specific metadata
      if (isTextFile) {
        formData.append('isTextFile', 'true');
        formData.append('textLanguage', metadata.textLanguage || 'tagalog');
      }

      // Add faculty attendance if provided
      if (metadata.facultyAttendance) {
        formData.append('facultyAttendance', metadata.facultyAttendance);
      }

      // Add meeting date if provided
      if (metadata.meetingDate) {
        formData.append('meetingDate', metadata.meetingDate);
      }

      // Add media duration if provided
      if (metadata.mediaDuration) {
        formData.append('mediaDuration', metadata.mediaDuration);
      }

      // Add progress reporting flag
      formData.append('reportProgress', 'true');

      console.log('Sending file to server:', {
        name: file.name,
        size: file.size,
        type: file.type,
        isVideo: isVideo,
        isTextFile: isTextFile,
        textLanguage: isTextFile ? (metadata.textLanguage || 'tagalog') : null
      });

      // Report upload starting
      if (onStage) onStage('Uploading', `Uploading ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)} MB)`);
      if (onProgress) onProgress(10);

      const response = await fetch(`${API_BASE_URL}/process-audio`, {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json',
        },
        credentials: 'include',
      });

      let initialData;
      try {
        initialData = await response.json();
      } catch (e) {
        console.error('Failed to parse server response:', e);
        throw new Error('Invalid server response');
      }

      if (!response.ok) {
        console.error('Server error response:', initialData);
        throw new Error(initialData.error || `Server error: ${response.status}`);
      }

      if (initialData.status === 'error') {
        throw new Error(initialData.error || 'Processing failed');
      }

      // Get the job ID from the initial response
      const jobId = initialData.jobId;
      if (!jobId) {
        throw new Error('No job ID received from server');
      }

      // Set up Server-Sent Events to receive progress updates
      console.log(`Setting up SSE connection for job ${jobId}`);

      // Create a promise that will resolve with the final result
      const resultPromise = new Promise((resolve, reject) => {
        // Create EventSource for SSE
        eventSource = new EventSource(`${API_BASE_URL}/progress/${jobId}`);

        // Handle progress events
        eventSource.onmessage = (event) => {
          try {
            const eventData = JSON.parse(event.data);
            console.log('SSE event:', eventData);

            if (eventData.type === 'progress') {
              // Update progress in UI
              if (onProgress && typeof eventData.progress === 'number') {
                onProgress(eventData.progress);
              }

              // Update stage in UI
              if (onStage && eventData.stage) {
                // Extract iteration information if available
                const iterationInfo = eventData.iteration && eventData.total_iterations
                  ? { iteration: eventData.iteration, totalIterations: eventData.total_iterations }
                  : null;

                // Pass both details and iteration info to the callback
                onStage(
                  eventData.stage,
                  eventData.details || '',
                  iterationInfo
                );
              }
            } else if (eventData.type === 'complete') {
              // Processing is complete, resolve with the result
              if (eventSource) {
                eventSource.close();
                eventSource = null;
              }

              // Report completion
              if (onStage) onStage('Completed', 'Processing complete! Redirecting to results page...');
              if (onProgress) onProgress(100);

              resolve(eventData.result);
            } else if (eventData.type === 'error') {
              // Error occurred, reject with the error
              if (eventSource) {
                eventSource.close();
                eventSource = null;
              }

              reject(new Error(eventData.error || 'Processing failed'));
            }
          } catch (e) {
            console.error('Error parsing SSE event:', e, event.data);
          }
        };

        // Handle connection errors
        eventSource.onerror = (error) => {
          console.error('SSE connection error:', error);
          if (eventSource) {
            eventSource.close();
            eventSource = null;
          }

          // If we haven't resolved yet, reject with the error
          reject(new Error('Connection to server lost'));
        };
      });

      // Wait for the final result
      const result = await resultPromise;

      // Enhance the response with file information
      if (result.result && result.result.data) {
        // Reuse the fileExtension and isVideo variables from earlier in the function
        result.result.data.original_format = fileExtension;

        // Set the file type
        if (isTextFile) {
          result.result.data.file_type = 'text';
          result.result.data.text_language = metadata.textLanguage || 'tagalog';
        } else {
          result.result.data.file_type = isVideo ? 'video' : 'audio';
        }
      }

      return result;

    } catch (error) {
      console.error('Audio processing error:', {
        message: error.message,
        stack: error.stack,
        file: {
          name: file.name,
          size: file.size,
          type: file.type
        }
      });
      throw error;
    }
  }
};

export { platform };




