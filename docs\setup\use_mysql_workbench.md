# Using MySQL Workbench Instead of phpMyAdmin

If you're having issues with phpMyAdmin due to Apache not starting, MySQL Workbench is an excellent alternative for managing your database.

## Step 1: Download and Install MySQL Workbench

1. Download MySQL Workbench from the official website:
   https://dev.mysql.com/downloads/workbench/

2. Install it following the installation wizard instructions

## Step 2: Connect to Your XAMPP MySQL Server

1. Open MySQL Workbench
2. Click on the "+" icon next to "MySQL Connections"
3. Configure the connection:
   - Connection Name: XAMPP MySQL
   - Connection Method: Standard (TCP/IP)
   - Hostname: 127.0.0.1
   - Port: 3307 (or whatever port your MySQL is using)
   - Username: root
   - Password: (leave empty if you haven't set a password)
4. Click "Test Connection" to verify it works
5. Click "OK" to save the connection

## Step 3: Create the Database and Tables

1. Connect to your MySQL server by clicking on the connection you created
2. Click on the "Create a new schema" icon in the toolbar
3. Enter "meeting_assistant" as the schema name and click "Apply"
4. Select the "meeting_assistant" schema in the left sidebar
5. <PERSON>lick on the "Create a new SQL tab for executing queries" icon
6. Past<PERSON> and execute the following SQL:

```sql
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  email VARCHAR(100) NOT NULL UNIQUE,
  first_name VARCHAR(50),
  last_name VARCHAR(50),
  profile_picture LONGTEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS minutes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  file_path VARCHAR(255),
  duration INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create a test user if needed
INSERT INTO users (username, password, email, first_name, last_name)
VALUES ('testuser', '$2b$10$6KhZKNbqUz.2vQxCQVJVDuLbwuGYwMpQAq7MbeMRzlRNKcWchMZHK', '<EMAIL>', 'Test', 'User');
```

## Step 4: Verify the Database Setup

1. In the left sidebar, right-click on "Tables" under the "meeting_assistant" schema and select "Refresh All"
2. You should see the "users" and "minutes" tables
3. Right-click on the "users" table and select "Select Rows - Limit 1000" to verify the table structure and data

## Step 5: Add the Profile Picture Column (if needed)

If the profile_picture column is not already in the users table:

1. Click on the "Create a new SQL tab for executing queries" icon
2. Execute the following SQL:

```sql
ALTER TABLE users ADD COLUMN profile_picture LONGTEXT;
```

## Advantages of MySQL Workbench

- Works independently of Apache
- More powerful features than phpMyAdmin
- Better visualization of database structure
- Advanced query editor with syntax highlighting
- Database modeling capabilities
