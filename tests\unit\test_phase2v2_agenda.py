"""
Test script for Phase2V2 with GPT-2 agenda identification
"""
import os
import sys
import time
from datetime import timedelta

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    """Test Phase2V2 with GPT-2 agenda identification."""
    start_time = time.time()

    print("Testing Phase2V2 with GPT-2 agenda identification...")

    # Import the necessary functions
    try:
        # First try the direct import
        try:
            from Pipeline_Phase2.Phase2V2 import classify_agenda_enhanced
            print("Successfully imported classify_agenda_enhanced from Pipeline_Phase2.Phase2V2")
        except ImportError:
            # Try alternative import path
            from python.Pipeline_Phase2.Phase2V2 import classify_agenda_enhanced
            print("Successfully imported classify_agenda_enhanced from python.Pipeline_Phase2.Phase2V2")
    except ImportError as e:
        print(f"Error importing classify_agenda_enhanced: {e}")
        print("Trying alternative approach...")

        # Try to import using a different approach
        try:
            # Add the specific directory to the path
            pipeline_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'Pipeline Phase2')
            sys.path.append(pipeline_dir)
            print(f"Added {pipeline_dir} to sys.path")

            # Try to import again
            from Phase2V2 import classify_agenda_enhanced
            print("Successfully imported classify_agenda_enhanced from Phase2V2")
        except ImportError as e:
            print(f"All import attempts failed: {e}")
            print("Available modules in sys.path:")
            for path in sys.path:
                print(f"  - {path}")
                if os.path.exists(path):
                    print(f"    Contents: {os.listdir(path)}")
            return

    # Create a sample transcript
    sample_transcript = """
    Dean Mia V. Villarica: Good morning everyone. Let's start with the first agenda item: Faculty Development.
    Speaker 2: The faculty training program is on track. We've completed the workshop design and scheduled the sessions.
    Dean Mia V. Villarica: Great. Any challenges or blockers?
    Speaker 2: No major issues, but we might need additional resources next month.

    Dean Mia V. Villarica: Moving on to our next topic, Budget Matters.
    Speaker 3: We're currently under budget by about 5%. The cost-saving measures implemented last quarter are working well.
    Dean Mia V. Villarica: That's excellent news. Any areas where we need to allocate additional funds?
    Speaker 3: The IT department has requested an increase for new equipment.

    Dean Mia V. Villarica: Now let's discuss the Curriculum Updates.
    Speaker 4: We've scheduled three curriculum review sessions for next month. The focus will be on updating the computer science program.
    Speaker 2: Will these changes affect the current students?
    Speaker 4: No, the changes will only apply to incoming students next academic year.
    """

    print("\nSample transcript:")
    print("-" * 50)
    print(sample_transcript)
    print("-" * 50)

    # Test agenda classification
    print("\nTesting agenda classification...")
    try:
        category = classify_agenda_enhanced(sample_transcript)
        print(f"Classified as: {category}")
        print("Classification successful!")
    except Exception as e:
        print(f"Error during classification: {e}")
        import traceback
        print(traceback.format_exc())
        print("Classification failed!")

    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nProcessing completed in {format_time(processing_time)}")

if __name__ == "__main__":
    main()
