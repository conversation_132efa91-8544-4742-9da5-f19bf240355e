#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify that both Tagalog and English transcripts are properly displayed
when processing text files.
"""

import os
import sys
import time
import json
import argparse

def main():
    print("Testing transcript display for text file processing...")
    
    # Create a sample text file in Tagalog
    sample_text = """
Tagapangulo: Magandang umaga sa inyong lahat. Magsisimula na ang ating pulong.
Kalihim: Salamat po. Nais kong ipabatid na kumpleto ang attendance ngayon.
Tagapangulo: Magaling. Simulan natin ang agenda para sa araw na ito.
Miyembro 1: Gusto kong iulat ang progreso ng ating proyekto. Nasa 75% na tayo ng completion.
Miyembro 2: Magandang balita yan. May mga challenges ba na dapat nating pagtuunan ng pansin?
Miyembro 1: Meron tayong ilang technical issues sa database, pero nakakahanap na kami ng solusyon.
Tagapangulo: Kailangan ba ninyo ng karagdagang resources?
Miyembro 1: Hindi na po, kaya na namin ito.
Tagapangulo: Magaling. Magpatuloy tayo sa susunod na agenda item.
"""
    
    # Save the sample text to a file
    test_dir = os.path.dirname(os.path.abspath(__file__))
    sample_file_path = os.path.join(test_dir, "sample_transcript_tagalog_test.txt")
    
    with open(sample_file_path, "w", encoding="utf-8") as f:
        f.write(sample_text)
    
    print(f"Created sample Tagalog transcript file at: {sample_file_path}")
    
    # Process the sample file using simple_text_processor.py
    simple_processor_path = os.path.join(test_dir, "simple_text_processor.py")
    
    # Create output directory
    output_dir = os.path.join(test_dir, "test_output_transcripts")
    os.makedirs(output_dir, exist_ok=True)
    
    # Build the command
    cmd = [
        sys.executable,
        simple_processor_path,
        sample_file_path,
        "--language", "tagalog",
        "--title", "Test Tagalog Transcript",
        "--output_dir", output_dir
    ]
    
    # Run the command
    print(f"Running command: {' '.join(cmd)}")
    start_time = time.time()
    
    import subprocess
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # Print output in real-time
    for line in process.stdout:
        print(line.strip())
    
    # Wait for the process to complete
    process.wait()
    
    # Print any errors
    for line in process.stderr:
        print(f"ERROR: {line.strip()}")
    
    # Check the result
    processing_time = time.time() - start_time
    print(f"Processing completed in {processing_time:.2f} seconds")
    
    # Check if the output files exist
    transcript_dir = os.path.join(output_dir, "transcription")
    main_transcript_dir = 'C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription'
    
    # Check the output directory
    if os.path.exists(transcript_dir):
        print(f"Transcript directory created: {transcript_dir}")
        transcript_files = os.listdir(transcript_dir)
        print(f"Transcript files in output directory: {transcript_files}")
        
        # Find Tagalog and English transcript files
        tagalog_files = [f for f in transcript_files if "_transcript_tagalog.txt" in f]
        english_files = [f for f in transcript_files if "_transcript.txt" in f and "_tagalog" not in f]
        
        if tagalog_files:
            print(f"Found Tagalog transcript file: {tagalog_files[0]}")
            tagalog_path = os.path.join(transcript_dir, tagalog_files[0])
            with open(tagalog_path, "r", encoding="utf-8") as f:
                tagalog_content = f.read()
            print(f"Tagalog content (first 200 chars): {tagalog_content[:200]}...")
        else:
            print("ERROR: No Tagalog transcript file found in output directory")
        
        if english_files:
            print(f"Found English transcript file: {english_files[0]}")
            english_path = os.path.join(transcript_dir, english_files[0])
            with open(english_path, "r", encoding="utf-8") as f:
                english_content = f.read()
            print(f"English content (first 200 chars): {english_content[:200]}...")
        else:
            print("ERROR: No English transcript file found in output directory")
    else:
        print(f"ERROR: Transcript directory not created: {transcript_dir}")
    
    # Check the main transcript directory
    if os.path.exists(main_transcript_dir):
        print(f"Main transcript directory exists: {main_transcript_dir}")
        
        # Get the list of files in the main transcript directory
        main_transcript_files = os.listdir(main_transcript_dir)
        
        # Filter files to find those with "Test Tagalog Transcript" in the name
        test_tagalog_files = [f for f in main_transcript_files if "Test_Tagalog_Transcript" in f or "Test Tagalog Transcript" in f]
        
        if test_tagalog_files:
            print(f"Found {len(test_tagalog_files)} test transcript files in main directory:")
            
            # Find Tagalog and English transcript files
            main_tagalog_files = [f for f in test_tagalog_files if "_transcript_tagalog.txt" in f]
            main_english_files = [f for f in test_tagalog_files if "_transcript.txt" in f and "_tagalog" not in f]
            
            if main_tagalog_files:
                print(f"Found Tagalog transcript file in main directory: {main_tagalog_files[0]}")
                main_tagalog_path = os.path.join(main_transcript_dir, main_tagalog_files[0])
                with open(main_tagalog_path, "r", encoding="utf-8") as f:
                    main_tagalog_content = f.read()
                print(f"Main Tagalog content (first 200 chars): {main_tagalog_content[:200]}...")
            else:
                print("ERROR: No Tagalog transcript file found in main directory")
            
            if main_english_files:
                print(f"Found English transcript file in main directory: {main_english_files[0]}")
                main_english_path = os.path.join(main_transcript_dir, main_english_files[0])
                with open(main_english_path, "r", encoding="utf-8") as f:
                    main_english_content = f.read()
                print(f"Main English content (first 200 chars): {main_english_content[:200]}...")
            else:
                print("ERROR: No English transcript file found in main directory")
        else:
            print(f"ERROR: No test transcript files found in main directory")
    else:
        print(f"ERROR: Main transcript directory does not exist: {main_transcript_dir}")
    
    # Clean up
    try:
        os.remove(sample_file_path)
        print(f"Removed sample file: {sample_file_path}")
    except Exception as e:
        print(f"Error removing sample file: {e}")
    
    print("Test completed.")

if __name__ == "__main__":
    main()
