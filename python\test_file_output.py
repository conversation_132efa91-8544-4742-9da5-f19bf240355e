"""
Simple test script to verify file output without loading models.
"""

import os
import sys
import json
import time
from datetime import datetime

def sanitize_json_data(data):
    """
    Recursively sanitize data to ensure it can be safely encoded as JSON.
    Handles problematic characters and non-serializable objects.
    """
    if isinstance(data, dict):
        return {k: sanitize_json_data(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [sanitize_json_data(item) for item in data]
    elif isinstance(data, str):
        # Replace problematic characters with safe alternatives
        return data.encode('ascii', 'replace').decode('ascii')
    elif isinstance(data, (int, float, bool, type(None))):
        # These types are JSON-serializable as is
        return data
    else:
        # Convert other types to strings
        return str(data)

def ensure_dirs():
    """Create output directories if they don't exist."""
    # Get the base directory (project root)
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    # Define the directories to create
    dirs = {
        'transcription': os.path.join(base_dir, 'python', 'transcription'),
        'minutes': os.path.join(base_dir, 'python', 'minutes of the meeting'),
        'uploads': os.path.join(base_dir, 'server', 'uploads'),
        'processed': os.path.join(base_dir, 'server', 'uploads', 'processed')
    }
    
    # Create each directory if it doesn't exist
    for name, path in dirs.items():
        if not os.path.exists(path):
            os.makedirs(path)
            print(f"Created directory: {path}")
    
    return dirs

def test_file_output():
    """Test file output without loading models."""
    # Ensure directories exist
    dirs = ensure_dirs()
    
    # Create a sample transcript
    transcript = [
        "Speaker 1: Hello, this is a test transcript.",
        "Speaker 2: This is used to test the output generation.",
        "Speaker 1: We want to make sure the JSON output is properly formatted.",
        "Speaker 3: And that the files are created in the right locations."
    ]
    
    # Create a sample minutes
    minutes = """# Meeting Minutes

## Executive Summary

This is a test meeting to verify output generation.

## Discussion Topics

### Testing

The team discussed testing the output generation process.

## Action Items

1. **Team** to verify output generation works correctly
"""
    
    # Create a unique job ID
    job_id = f"test_{int(time.time())}"
    
    # Create file paths
    transcript_dir = dirs['transcription']
    minutes_dir = dirs['minutes']
    processed_dir = dirs['processed']
    
    transcript_file = os.path.join(transcript_dir, f"{job_id}_transcript.txt")
    minutes_file = os.path.join(minutes_dir, f"{job_id}_minutes.md")
    json_file = os.path.join(processed_dir, f"{job_id}_result.json")
    
    # Write the transcript file
    try:
        with open(transcript_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(transcript))
        print(f"Created transcript file: {transcript_file}")
    except Exception as e:
        print(f"Error creating transcript file: {e}")
    
    # Write the minutes file
    try:
        with open(minutes_file, 'w', encoding='utf-8') as f:
            f.write(minutes)
        print(f"Created minutes file: {minutes_file}")
    except Exception as e:
        print(f"Error creating minutes file: {e}")
    
    # Create the result object
    result = {
        "status": "success",
        "data": {
            "transcript": '\n'.join(transcript),
            "minutes": minutes,
            "transcript_file": transcript_file,
            "minutes_file": minutes_file,
            "speakers": ["Speaker 1", "Speaker 2", "Speaker 3"],
            "processing_time": 10.5,
            "processing_time_formatted": "0:00:10",
            "job_id": job_id,
            "title": "Test Meeting",
            "description": "Test Description",
            "user_id": "test_user"
        }
    }
    
    # Add some problematic characters to test sanitization
    result["data"]["transcript"] += "\nSpeaker 4: This contains some problematic characters: 😊 ñ é ü"
    
    # Sanitize the result
    sanitized_result = sanitize_json_data(result)
    
    # Convert to JSON
    json_result = json.dumps(sanitized_result, ensure_ascii=True, indent=2)
    
    # Write the JSON result to a file
    try:
        with open(json_file, 'w', encoding='utf-8') as f:
            f.write(json_result)
        print(f"Created JSON file: {json_file}")
    except Exception as e:
        print(f"Error creating JSON file: {e}")
    
    # Print the JSON result
    print("\nJSON Result:")
    print(json_result)
    
    return {
        "transcript_file": transcript_file,
        "minutes_file": minutes_file,
        "json_file": json_file
    }

if __name__ == "__main__":
    print("Testing file output...")
    files = test_file_output()
    print("\nDone.")
