require('dotenv').config();
const mysql = require('mysql2/promise');

async function verifyDatabase() {
  let connection;
  
  try {
    // Connect to MySQL
    console.log('Connecting to MySQL...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3307,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'meeting_assistant'
    });
    
    console.log('Connected to MySQL successfully!');
    
    // Check users table
    console.log('\nChecking users table...');
    const [userRows] = await connection.execute('DESCRIBE users');
    console.log('Users table structure:');
    userRows.forEach(row => {
      console.log(`- ${row.Field}: ${row.Type} ${row.Null === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // Check if profile_picture column exists
    const profilePictureColumn = userRows.find(row => row.Field === 'profile_picture');
    if (profilePictureColumn) {
      console.log('\n✅ profile_picture column exists in users table!');
    } else {
      console.log('\n❌ profile_picture column does NOT exist in users table!');
    }
    
    // Check minutes table
    console.log('\nChecking minutes table...');
    const [minuteRows] = await connection.execute('DESCRIBE minutes');
    console.log('Minutes table structure:');
    minuteRows.forEach(row => {
      console.log(`- ${row.Field}: ${row.Type} ${row.Null === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // Count users
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    console.log(`\nNumber of users in database: ${userCount[0].count}`);
    
    // Count minutes
    const [minuteCount] = await connection.execute('SELECT COUNT(*) as count FROM minutes');
    console.log(`Number of minutes in database: ${minuteCount[0].count}`);
    
    console.log('\nDatabase verification completed successfully!');
    
  } catch (error) {
    console.error('Error verifying database:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

verifyDatabase();
