import React, { useState, useEffect } from 'react';
import { logAllTranscripts, logAllMinutes, checkTranscriptMinuteLinks, fixMissingMinutes } from '../utils/debugStorage';
import { forceSyncTranscriptsToMinutes, checkForMissingMinutes } from '../utils/forceSync';
import { synchronizeAll } from '../utils/syncService';

const DebugStorage = () => {
  const [transcripts, setTranscripts] = useState([]);
  const [minutes, setMinutes] = useState([]);
  const [linkResults, setLinkResults] = useState(null);
  const [fixResults, setFixResults] = useState(null);
  const [syncResults, setSyncResults] = useState(null);

  useEffect(() => {
    // Load data on mount
    loadData();
  }, []);

  const loadData = () => {
    try {
      // Log all transcripts and minutes
      const transcriptsData = logAllTranscripts();
      const minutesData = logAllMinutes();
      
      // Check links
      const links = checkTranscriptMinuteLinks();
      
      // Update state
      setTranscripts(transcriptsData);
      setMinutes(minutesData);
      setLinkResults(links);
    } catch (error) {
      console.error('Error loading debug data:', error);
    }
  };

  const handleFixMissingMinutes = () => {
    try {
      const results = fixMissingMinutes();
      setFixResults(results);
      
      // Reload data after fixing
      loadData();
    } catch (error) {
      console.error('Error fixing missing minutes:', error);
      setFixResults({ error: error.message });
    }
  };

  const handleForceSync = () => {
    try {
      // First, run synchronizeAll
      synchronizeAll();
      
      // Then force sync transcripts to minutes
      const syncStats = forceSyncTranscriptsToMinutes();
      
      // Check for missing minutes
      const missingStats = checkForMissingMinutes();
      
      // Update state
      setSyncResults({
        syncStats,
        missingStats
      });
      
      // Reload data after syncing
      loadData();
    } catch (error) {
      console.error('Error during force sync:', error);
      setSyncResults({ error: error.message });
    }
  };

  const handleClearAll = () => {
    if (window.confirm('Are you sure you want to clear all localStorage data? This cannot be undone.')) {
      localStorage.clear();
      loadData();
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Debug Storage</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={loadData}
          style={{ 
            padding: '10px 15px', 
            marginRight: '10px',
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh Data
        </button>
        
        <button 
          onClick={handleFixMissingMinutes}
          style={{ 
            padding: '10px 15px', 
            marginRight: '10px',
            backgroundColor: '#2196F3',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Fix Missing Minutes
        </button>
        
        <button 
          onClick={handleForceSync}
          style={{ 
            padding: '10px 15px', 
            marginRight: '10px',
            backgroundColor: '#FF9800',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Force Sync
        </button>
        
        <button 
          onClick={handleClearAll}
          style={{ 
            padding: '10px 15px',
            backgroundColor: '#F44336',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Clear All Data
        </button>
      </div>
      
      {/* Results Section */}
      <div style={{ marginBottom: '20px' }}>
        <h2>Link Check Results</h2>
        {linkResults ? (
          <div style={{ 
            backgroundColor: '#f5f5f5', 
            padding: '15px', 
            borderRadius: '4px',
            marginBottom: '20px'
          }}>
            <p><strong>Total Transcripts:</strong> {linkResults.total}</p>
            <p><strong>Matched with Minutes:</strong> {linkResults.matched}</p>
            <p><strong>Missing Minutes:</strong> {linkResults.missingMinutes}</p>
            
            {linkResults.missingMinutes > 0 && (
              <div>
                <h3>Transcripts Without Minutes:</h3>
                <ul>
                  {linkResults.transcriptsWithoutMinutes.map((item, index) => (
                    <li key={index}>
                      ID: {item.id}, Title: {item.title}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <p>No link check results available</p>
        )}
        
        {fixResults && (
          <div style={{ 
            backgroundColor: fixResults.error ? '#ffebee' : '#e8f5e9', 
            padding: '15px', 
            borderRadius: '4px',
            marginBottom: '20px'
          }}>
            <h3>Fix Results:</h3>
            {fixResults.error ? (
              <p style={{ color: '#d32f2f' }}><strong>Error:</strong> {fixResults.error}</p>
            ) : (
              <p><strong>Fixed Minutes:</strong> {fixResults.fixed}</p>
            )}
          </div>
        )}
        
        {syncResults && (
          <div style={{ 
            backgroundColor: syncResults.error ? '#ffebee' : '#e8f5e9', 
            padding: '15px', 
            borderRadius: '4px',
            marginBottom: '20px'
          }}>
            <h3>Sync Results:</h3>
            {syncResults.error ? (
              <p style={{ color: '#d32f2f' }}><strong>Error:</strong> {syncResults.error}</p>
            ) : (
              <div>
                <h4>Sync Stats:</h4>
                <p><strong>Total:</strong> {syncResults.syncStats.total}</p>
                <p><strong>Created:</strong> {syncResults.syncStats.created}</p>
                <p><strong>Updated:</strong> {syncResults.syncStats.updated}</p>
                <p><strong>Skipped:</strong> {syncResults.syncStats.skipped}</p>
                <p><strong>Errors:</strong> {syncResults.syncStats.errors}</p>
                
                <h4>Missing Stats:</h4>
                <p><strong>Total:</strong> {syncResults.missingStats.total}</p>
                <p><strong>Missing:</strong> {syncResults.missingStats.missing}</p>
                <p><strong>Created:</strong> {syncResults.missingStats.created}</p>
                <p><strong>Errors:</strong> {syncResults.missingStats.errors}</p>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Transcripts Section */}
      <div style={{ marginBottom: '20px' }}>
        <h2>Transcripts ({transcripts.length})</h2>
        {transcripts.length > 0 ? (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f5f5f5' }}>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>ID</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Title</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Has Transcript</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Has Minutes</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Created At</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Updated At</th>
              </tr>
            </thead>
            <tbody>
              {transcripts.map((transcript, index) => (
                <tr key={index} style={{ backgroundColor: index % 2 === 0 ? 'white' : '#f9f9f9' }}>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{transcript.id}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{transcript.title}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{transcript.transcript ? 'Yes' : 'No'}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{transcript.minutes ? 'Yes' : 'No'}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{transcript.createdAt}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{transcript.updatedAt}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>No transcripts found</p>
        )}
      </div>
      
      {/* Minutes Section */}
      <div>
        <h2>Minutes ({minutes.length})</h2>
        {minutes.length > 0 ? (
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f5f5f5' }}>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>ID</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Title</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Has Content</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Transcript ID</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Created At</th>
                <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Updated At</th>
              </tr>
            </thead>
            <tbody>
              {minutes.map((minute, index) => (
                <tr key={index} style={{ backgroundColor: index % 2 === 0 ? 'white' : '#f9f9f9' }}>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{minute.id}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{minute.title}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{minute.content ? 'Yes' : 'No'}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{minute.transcript_id}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{minute.createdAt}</td>
                  <td style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>{minute.updatedAt}</td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p>No minutes found</p>
        )}
      </div>
    </div>
  );
};

export default DebugStorage;
