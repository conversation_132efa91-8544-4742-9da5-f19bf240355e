# Thesis Project Information

## Official Title
**DEAN'S OFFICE AI-POWERED MEETING ASSISTANT FOR COLLEGE OF COMPUTER STUDIES AT LAGUNA STATE POLYTECHNIC UNIVERSITY STA. CRUZ CAMPUS**

## Academic Details

### Institution Information
- **University**: Laguna State Polytechnic University – Santa Cruz Campus
- **Address**: Bubukal, Santa Cruz, Laguna
- **College**: College of Computer Studies
- **Academic Year**: 2024-2025
- **Submission Date**: May 2025

### Degree Program
- **Degree**: Bachelor of Science in Computer Science
- **Major**: Intelligent Systems
- **Project Type**: Undergraduate Thesis

### Research Team

#### Students (Researchers)
1. **BERCADES, JOHN RICHARD L.**
2. **NIEGOS, BARON DENVER D.**
3. **VENANCIO, NICKO A.**

#### Thesis Supervisor
- **Dean MIA V. VILLARICA, D.I.T.**
  - Position: Dean, College of Computer Studies
  - Qualification: Doctor in Information Technology

## Project Overview

### Problem Statement
The Dean's Office at the College of Computer Studies requires an efficient system for managing meeting documentation, transcription, and minutes generation to improve administrative efficiency and record-keeping accuracy.

### Objectives
1. **Primary Objective**: Develop an AI-powered meeting assistant system specifically for the Dean's Office
2. **Secondary Objectives**:
   - Implement automated transcription using advanced speech recognition
   - Generate structured meeting minutes using AI technologies
   - Provide multi-language support (English and Tagalog)
   - Create a user-friendly web interface for faculty use
   - Implement attendance tracking for faculty meetings

### Scope and Limitations
- **Scope**: Dean's Office meeting management for College of Computer Studies
- **Target Users**: Dean, faculty members, administrative staff
- **Languages Supported**: English and Tagalog
- **Platform**: Web-based application

### Technical Approach
- **Architecture**: Clean Architecture principles
- **Frontend**: React.js with modern UI components
- **Backend**: Node.js with Express server
- **AI Processing**: Python with PyTorch/TensorFlow
- **Database**: MySQL for data storage
- **Document Generation**: DOCX export with customizable templates

## Research Significance

### Academic Contribution
This thesis demonstrates the practical application of AI technologies in educational administration, specifically addressing real-world challenges in meeting management and documentation.

### Institutional Impact
The system directly benefits the College of Computer Studies by:
- Improving meeting documentation efficiency
- Reducing manual transcription workload
- Enhancing record-keeping accuracy
- Streamlining administrative processes

### Technical Innovation
- Implementation of clean architecture in AI-powered applications
- Integration of multiple AI technologies for comprehensive meeting assistance
- Multi-language processing capabilities
-  transcription and automated minutes generation

## Repository Structure for Academic Review

The codebase is organized following clean architecture principles to facilitate:
- **Code Review**: Clear separation of concerns for academic evaluation
- **Documentation**: Comprehensive documentation for thesis defense
- **Reproducibility**: Well-structured code for future research and development
- **Maintenance**: Scalable architecture for potential institutional adoption

## Future Research Directions

1. **Enhanced AI Models**: Integration of more advanced language models
2. **Extended Language Support**: Additional Philippine languages
3. **Mobile Application**: Development of mobile companion app
4. **Integration**: Connection with existing university systems
5. **Analytics**: Meeting insights and reporting features

---

*This document serves as the official academic record for the thesis project and should be referenced in all academic submissions and presentations.*
