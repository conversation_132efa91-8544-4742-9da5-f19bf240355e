"""
Simple test script for the transcript enhancement feature
"""
import os
import sys
import time

# Force CPU usage for testing
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# Add the Pipeline Phase2 directory to the path
sys.path.append('Pipeline Phase2')

# Create a sample transcript
sample_transcript = """
Speaker 1: Good morning everyone um I would like to start the meeting by discussing the project timeline.
Speaker 2: Yes, I think we should uh focus on the deliverables for next week.
Speaker 1: I agree. We need to complete the first phase by Friday.
Speaker 2: What about the budget concerns that were raised in the last meeting?
Speaker 1: We will address those after we finalize the timeline.
"""

# Import just the functions we need
from Pipeline_Phase2.Phase2V2 import basic_transcript_cleaning

# Test basic cleaning
print("\nTesting basic transcript cleaning...")
basic_cleaned = basic_transcript_cleaning(sample_transcript)
print("\nBasic cleaned transcript:")
print("-" * 50)
print(basic_cleaned)
print("-" * 50)

print("Test completed successfully!")
