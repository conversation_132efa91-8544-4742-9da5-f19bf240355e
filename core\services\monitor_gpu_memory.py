"""
Monitor GPU memory usage during processing.
This script provides real-time monitoring of GPU memory usage.
"""

import os
import sys
import time
import argparse
import threading
import psutil
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def monitor_memory(interval=5.0, log_file=None):
    """
    Monitor system and GPU memory usage at regular intervals.
    
    Args:
        interval (float): Time between measurements in seconds
        log_file (str): Path to log file (if None, print to console)
    """
    try:
        import torch
        has_torch = torch.cuda.is_available()
    except ImportError:
        has_torch = False
    
    # Create log file if specified
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        f = open(log_file, 'w')
        f.write("Timestamp,RAM_Used_GB,RAM_Total_GB,RAM_Percent")
        if has_torch:
            f.write(",GPU_Used_GB,GPU_Reserved_GB,GPU_Total_GB,GPU_Used_Percent,GPU_Reserved_Percent\n")
        else:
            f.write("\n")
    
    try:
        while True:
            # Get current time
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Get system RAM usage
            system_ram = psutil.virtual_memory()
            ram_used_gb = system_ram.used / (1024**3)
            ram_total_gb = system_ram.total / (1024**3)
            ram_percent = system_ram.percent
            
            # Format output
            output = f"{timestamp},{ram_used_gb:.2f},{ram_total_gb:.2f},{ram_percent:.1f}"
            
            # Get GPU memory usage if available
            if has_torch:
                gpu_used_gb = torch.cuda.memory_allocated() / (1024**3)
                gpu_reserved_gb = torch.cuda.memory_reserved() / (1024**3)
                gpu_total_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
                gpu_used_percent = (gpu_used_gb / gpu_total_gb) * 100
                gpu_reserved_percent = (gpu_reserved_gb / gpu_total_gb) * 100
                
                # Add GPU info to output
                output += f",{gpu_used_gb:.2f},{gpu_reserved_gb:.2f},{gpu_total_gb:.2f},{gpu_used_percent:.1f},{gpu_reserved_percent:.1f}"
            
            # Write to log file or print to console
            if log_file:
                f.write(output + "\n")
                f.flush()
            else:
                print("\n" + "=" * 50)
                print(f"Memory Usage at {timestamp}")
                print("-" * 50)
                print(f"RAM: {ram_used_gb:.2f} GB / {ram_total_gb:.2f} GB ({ram_percent:.1f}%)")
                
                if has_torch:
                    print(f"GPU: {gpu_used_gb:.2f} GB / {gpu_total_gb:.2f} GB ({gpu_used_percent:.1f}%)")
                    print(f"GPU Reserved: {gpu_reserved_gb:.2f} GB ({gpu_reserved_percent:.1f}%)")
                
                print("=" * 50)
            
            # Wait for next interval
            time.sleep(interval)
    
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
    finally:
        if log_file and 'f' in locals():
            f.close()

def start_monitoring_thread(interval=5.0, log_file=None):
    """
    Start memory monitoring in a background thread.
    
    Args:
        interval (float): Time between measurements in seconds
        log_file (str): Path to log file (if None, print to console)
        
    Returns:
        threading.Thread: The monitoring thread
    """
    monitor_thread = threading.Thread(
        target=monitor_memory,
        args=(interval, log_file),
        daemon=True
    )
    monitor_thread.start()
    return monitor_thread

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Monitor GPU and system memory usage")
    parser.add_argument("--interval", type=float, default=5.0, help="Time between measurements in seconds")
    parser.add_argument("--log", help="Path to log file (if not specified, print to console)")
    
    args = parser.parse_args()
    
    print(f"Starting memory monitoring (interval: {args.interval}s)")
    if args.log:
        print(f"Logging to: {args.log}")
    
    # Start monitoring
    monitor_memory(args.interval, args.log)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
