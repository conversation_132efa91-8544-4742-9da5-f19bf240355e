import os
import sys
import time
from datetime import timedelta

# Add the Pipeline Phase2 directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase2'))

# Force CPU usage for PyTorch to avoid GPU memory issues
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'

# Set memory limits for TensorFlow
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# Set PyTorch memory efficiency settings
os.environ['OMP_NUM_THREADS'] = '4'
os.environ['MKL_NUM_THREADS'] = '4'

# Import Phase2V2
from Phase2V2 import process_meeting_transcript_enhanced

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    # Very small sample transcript for testing
    sample_transcript = """
    Speaker 1: Welcome to our meeting. Today we'll discuss the budget.
    Speaker 2: I think we need to allocate more funds to research.
    Speaker 1: Agreed. Let's increase the research budget by 10%.
    """
    
    print("Testing Phase2V2 processing with small sample...")
    start_time = time.time()
    
    # Process the transcript
    try:
        minutes = process_meeting_transcript_enhanced(
            sample_transcript,
            include_executive_summary=True,
            enhance_transcript=False  # Set to False to avoid potential issues
        )
        
        # Print the result
        print("\nGenerated Minutes:")
        print("-----------------")
        print(minutes)
        
        # Print processing time
        processing_time = time.time() - start_time
        print(f"\nProcessing completed in {format_time(processing_time)}")
        print("Test successful!")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        print(traceback.format_exc())
        print("Test failed!")

if __name__ == "__main__":
    main()
