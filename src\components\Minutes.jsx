import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Sidebar from './Sidebar';
import BackIcon from '../assets/back.png';
// Add markdown parser
import ReactMarkdown from 'react-markdown';
// Import sanitization utility
import { sanitizeContent, safeParseJson, removeNamesInBrackets } from '../utils/sanitizeContent';
// Import docx for document generation
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';
// Import synchronization services
import { synchronizeAll, syncTranscriptToMinutes } from '../utils/syncService';
// Import attendance service
import { getAttendanceForMeeting, processAttendanceData, createDefaultAttendanceData, saveAttendanceForMeeting } from '../utils/attendanceService';
// Import file path resolver
import { fetchFileContent, normalizePath } from '../utils/filePathResolver';
// Import minute storage utilities
import { getMinuteById, getAllMinutes } from '../utils/minuteStorage';

const Minutes = () => {
  const navigate = useNavigate();
  const { id } = useParams(); // Get the ID from the URL
  const [minuteData, setMinuteData] = useState({
    dateTime: new Date().toLocaleString(),
    meetingDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
    attendees: [],
    actionItems: [],
    agenda: "Review previous meeting minutes\nDiscuss department budget\nPlan for upcoming semester\nFaculty concerns and suggestions",
    openingStatement: "",
    facilitator: "",
    meetingTime: "",
    meetingPlatform: ""
  });
  const [attendanceData, setAttendanceData] = useState([]);
  const [minutesContent, setMinutesContent] = useState('');
  const [minutesFile, setMinutesFile] = useState('');
  const [title, setTitle] = useState('Minutes of the Meeting');
  const [loading, setLoading] = useState(true);

  // Function to filter out the Key Participants section, remove names in square brackets, and fix question marks in the minutes content
  const filterKeyParticipants = (content) => {
    if (!content) return 'No content available';

    console.log('Minutes: Filtering content, original length:', content.length);
    console.log('Minutes: First 100 characters:', content.substring(0, 100));

    // First, remove all names in square brackets
    content = removeNamesInBrackets(content);
    console.log('Minutes: After removing names in brackets, length:', content.length);

    // Check if content is already in Calibri, italic format (from previous processing)
    if (content.includes('font-family: Calibri') || content.includes('font-style: italic')) {
      console.log('Minutes: Content appears to already have formatting applied');
    }

    // Split the content by lines
    const lines = content.split('\n');
    console.log('Minutes: Content has', lines.length, 'lines');

    const filteredLines = [];
    let skipLine = false;

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];

      // If we find a line with "Key Participants", start skipping
      if (line.includes('Key Participants')) {
        skipLine = true;
        continue;
      }

      // If we're skipping and find a new section (starts with ##) or a blank line followed by a non-blank line
      // that doesn't start with a list item, stop skipping
      if (skipLine &&
          (line.startsWith('##') ||
           (line.trim() === '' && i + 1 < lines.length && lines[i + 1].trim() !== '' && !lines[i + 1].trim().startsWith('-')))) {
        skipLine = false;
      }

      // Fix question marks in the executive summary
      if (line.includes('Executive Summary') || line.includes('Key Topics Discussed')) {
        // Don't modify these header lines
      } else if (line.includes('?')) {
        // Replace question marks that appear to be formatting errors with periods
        line = line.replace(/\? ([A-Z])/g, '. $1');
      }

      // Add the line if we're not skipping
      if (!skipLine) {
        filteredLines.push(line);
      }
    }

    const result = filteredLines.join('\n');
    console.log('Minutes: Filtered content length:', result.length);
    console.log('Minutes: Removed', lines.length - filteredLines.length, 'lines');

    // If content is very short after filtering, log a warning
    if (result.length < 50) {
      console.warn('Minutes: Content is very short after filtering!', result);
    }

    return result;
  };

  // Auto-generate opening statement when attendanceData or title changes
  useEffect(() => {
    // Generate a sample opening statement based on available data
    const platform = "online via Zoom";
    const date = minuteData.meetingDate || new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
    const facilitator = "the College of Computer Studies Associate Dean";

    let attendeesList = "";
    if (attendanceData.length > 0) {
      const presentAttendees = attendanceData.filter(person => person.status === 'present');
      if (presentAttendees.length > 0) {
        // If we have many attendees, group them by role instead of listing all names
        attendeesList = "with the participation of the regular and part-time faculty members, SOCCS Officers, and OJT students";
      } else {
        attendeesList = "with all attendees present";
      }
    } else {
      attendeesList = "with all attendees present";
    }

    const statement = `The ${title} held ${platform} on ${date}. It was facilitated by ${facilitator}, ${attendeesList}.`;

    setMinuteData(prevData => ({
      ...prevData,
      openingStatement: statement,
      meetingPlatform: platform,
      facilitator: facilitator
    }));

  }, [attendanceData, title, minuteData.meetingDate]);

  useEffect(() => {
    console.log('Minutes: Component mounted, synchronizing data');
    console.log('Minutes: URL ID parameter:', id);

    // First, synchronize all data to ensure consistency
    synchronizeAll();

    // Ensure we have attendance data for this meeting
    const ensureAttendanceData = () => {
      // Try to get attendance data from our attendance service first
      let attendanceData = [];
      if (title) {
        console.log('Minutes: Auto-loading attendance data for meeting:', title);
        attendanceData = getAttendanceForMeeting(title, id);
      }

      // If we don't have attendance data, create default data
      if (!attendanceData || attendanceData.length === 0) {
        console.log('Minutes: No attendance data found, creating default attendance data');

        // Create default attendance data with all faculty present
        const defaultAttendance = createDefaultAttendanceData();

        // Save this default attendance for the current meeting
        if (title) {
          console.log('Minutes: Saving default attendance for meeting:', title);
          saveAttendanceForMeeting(title, defaultAttendance, id);
        }

        // Set the attendance data in the component state
        setAttendanceData(defaultAttendance);
      } else {
        console.log('Minutes: Found existing attendance data:', attendanceData.length, 'records');

        // Process the attendance data to ensure it has all required fields
        const processedAttendance = processAttendanceData(attendanceData);
        setAttendanceData(processedAttendance);
      }
    };

    // Try to load the minute based on the ID from the URL
    if (id) {
      console.log('Minutes: Attempting to load minute with ID:', id);

      // Try to find the minute in localStorage first
      const minute = getMinuteById(id);

      if (minute) {
        console.log('Minutes: Found minute in localStorage:', minute);

        // Set the title and other data from the minute
        setTitle(minute.title || 'Minutes of the Meeting');

        // If the minute has content, use it directly
        if (minute.content) {
          console.log('Minutes: Using content from minute object');
          setMinutesContent(minute.content);

          // Ensure we have attendance data for this minute
          ensureAttendanceData();

          setLoading(false);
          return; // Exit early since we found the minute
        }
      } else {
        console.log('Minutes: No minute found with ID:', id);

        // Try to find a minute with a matching title (for backward compatibility)
        const allMinutes = getAllMinutes();
        const minuteByTitle = allMinutes.find(m =>
          m.title && m.title.toLowerCase().replace(/\s+/g, '-') === id.toLowerCase()
        );

        if (minuteByTitle) {
          console.log('Minutes: Found minute by title match:', minuteByTitle);

          // Set the title and other data from the minute
          setTitle(minuteByTitle.title || 'Minutes of the Meeting');

          // If the minute has content, use it directly
          if (minuteByTitle.content) {
            console.log('Minutes: Using content from minute object');
            setMinutesContent(minuteByTitle.content);

            // Ensure we have attendance data for this minute
            ensureAttendanceData();

            setLoading(false);
            return; // Exit early since we found the minute
          }
        }
      }
    }

    // If we couldn't find a minute by ID or title, fall back to the current transcript
    console.log('Minutes: Falling back to currentTranscript in localStorage');
    const storedTranscript = localStorage.getItem('currentTranscript');
    if (storedTranscript) {
      try {
        // Use safeParseJson to handle potential encoding issues
        const transcriptData = safeParseJson(storedTranscript, {});
        console.log('Minutes: Found transcript data in localStorage');

        // Set minutes content if available
        if (transcriptData.minutes) {
          console.log('Found minutes content:', transcriptData.minutes);

          // Check if the minutes content is a placeholder indicating we need to fetch from file
          if (transcriptData.minutes === "FETCH_FROM_FILE") {
            console.log('Minutes content is a placeholder, fetching from file...');

            // If we have a minutes file path, try to read it directly
            if (transcriptData.minutes_file) {
              console.log('Fetching minutes from file path:', transcriptData.minutes_file);

              // Check if the file path exists (less strict validation)
              if (typeof transcriptData.minutes_file === 'string' &&
                  transcriptData.minutes_file.trim() !== '') {

                // Try to read the file directly
                setLoading(true); // Set loading state while fetching

                // Use our file path resolver to normalize the path
                console.log('Minutes: Using filePathResolver to normalize path');
                const normalizedPath = normalizePath(transcriptData.minutes_file, {
                  jobId: transcriptData.job_id,
                  fileType: 'minutes'
                });
                console.log('Minutes: Normalized path:', normalizedPath);

                console.log('Minutes: Using fetchFileContent utility to get file content');

                // Use our fetchFileContent utility
                fetchFileContent(normalizedPath, {
                  baseUrl: 'http://localhost:3001/api',
                  endpoint: 'file'
                })
                  .then(minutesText => {
                    console.log('Minutes: Successfully fetched minutes content, length:', minutesText.length);
                    if (minutesText.trim() === '') {
                      throw new Error('Minutes file is empty');
                    }

                    // Sanitize the minutes content to handle encoding issues
                    const sanitizedMinutes = sanitizeContent(minutesText);
                    setMinutesContent(sanitizedMinutes);

                    // Update localStorage with the sanitized minutes content and normalized path
                    transcriptData.minutes = sanitizedMinutes;
                    transcriptData.minutes_file = normalizedPath;
                    localStorage.setItem('currentTranscript', JSON.stringify(transcriptData));

                    // Also sync this transcript to minutes to ensure consistency
                    syncTranscriptToMinutes(transcriptData);
                  })
                  .catch(err => {
                    console.error('Error fetching minutes file:', err);

                    // Try to recover by using any minutes content we might have
                    if (transcriptData.minutes && transcriptData.minutes !== "FETCH_FROM_FILE") {
                      console.log('Using minutes content from localStorage as fallback');
                      setMinutesContent(transcriptData.minutes);
                    } else {
                      // Try to get the actual processed minutes from the most recent upload
                      // First, check if we have a job_id in the transcriptData
                      let validPath = '';
                      if (transcriptData.job_id) {
                        validPath = `uploads/processed/${transcriptData.job_id}/minutes of the meeting/minutes.md`;
                        console.log('Using job_id for path:', validPath);
                      } else {
                          // Try to get the latest minutes file from the server
                        console.log('Minutes: Fetching latest minutes using fetchLatestMinutes utility');
                        fetchLatestMinutes({
                          baseUrl: 'http://localhost:3001/api'
                        })
                        .then(data => {
                          if (data.status === 'success' && data.content) {
                            console.log('Successfully fetched latest minutes from server');
                            console.log('Minutes file path:', data.filePath);

                            // Set the minutes content
                            setMinutesContent(data.content);

                            // Update localStorage with the actual minutes content and file path
                            transcriptData.minutes = data.content;
                            transcriptData.minutes_file = data.filePath;
                            localStorage.setItem('currentTranscript', JSON.stringify(transcriptData));

                            setLoading(false);
                          } else {
                            throw new Error(data.error || 'Failed to fetch latest minutes');
                          }
                        })
                        .catch(latestErr => {
                          console.error('Error fetching latest minutes:', latestErr);

                          // Try to find the most recent minute from the database as fallback
                          const userId = localStorage.getItem('userId');
                          if (userId) {
                            console.log('Attempting to fetch most recent minute for user:', userId);
                            fetch(`http://localhost:3001/api/minutes/${userId}`, {
                              credentials: 'include',
                              headers: {
                                'Accept': 'application/json',
                              }
                            })
                            .then(response => response.json())
                            .then(data => {
                              if (data.status === 'success' && data.minutes && data.minutes.length > 0) {
                                // Get the most recent minute
                                const recentMinute = data.minutes[0];
                                console.log('Found recent minute:', recentMinute);

                                if (recentMinute.file_path) {
                                  console.log('Using file path from recent minute:', recentMinute.file_path);
                                  return fetch(`http://localhost:3001/api/file?path=${encodeURIComponent(recentMinute.file_path)}`);
                                }
                              }
                              throw new Error('No recent minutes found');
                            })
                            .then(response => {
                              if (!response.ok) {
                                throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
                              }
                              return response.text();
                            })
                            .then(minutesText => {
                              console.log('Successfully fetched minutes from recent minute');
                              setMinutesContent(minutesText);
                              setLoading(false);
                            })
                            .catch(recentErr => {
                              console.error('Error fetching recent minute:', recentErr);
                              // Use the most recent processed file
                              validPath = 'uploads/processed/transcript_1745446833121/minutes of the meeting/minutes.md';
                              console.log('Using most recent processed file path:', validPath);
                            });
                          } else {
                            // Use the most recent processed file
                            validPath = 'uploads/processed/transcript_1745446833121/minutes of the meeting/minutes.md';
                            console.log('Using most recent processed file path:', validPath);
                          }
                        });
                        return; // Exit early as we're handling this asynchronously
                      }

                      // Try to fetch from the valid path using our utility
                      console.log('Minutes: Fetching from valid path using fetchFileContent utility');
                      fetchFileContent(validPath, {
                        baseUrl: 'http://localhost:3001/api',
                        endpoint: 'file'
                      })
                        .then(minutesText => {
                          console.log('Successfully fetched minutes from valid path');
                          setMinutesContent(minutesText);

                          // Update localStorage with the valid path
                          transcriptData.minutes_file = validPath;
                          localStorage.setItem('currentTranscript', JSON.stringify(transcriptData));
                        })
                        .catch(fallbackErr => {
                          console.error('Fallback fetch also failed:', fallbackErr);

                          // Try to check if the file exists in the main minutes directory
                          console.log('Trying to find minutes file in main minutes directory');

                          // Extract the filename from the path
                          const pathParts = validPath.split('/');
                          const filename = pathParts[pathParts.length - 1];

                          // Try to find the file in the main minutes directory
                          fetch(`http://localhost:3001/api/list-files?path=python/minutes of the meeting`)
                            .then(response => response.json())
                            .then(files => {
                              console.log('Files in main minutes directory:', files);

                              // Look for a file that matches our filename or contains the meeting title
                              let matchingFile = null;

                              if (files && Array.isArray(files)) {
                                // First try to find an exact match
                                matchingFile = files.find(file => file === filename);

                                // If no exact match, try to find a file that contains the meeting title
                                if (!matchingFile && title) {
                                  const titleParts = title.split(' ');
                                  matchingFile = files.find(file => {
                                    // Check if the file contains any part of the title
                                    return titleParts.some(part =>
                                      part.length > 3 && file.toLowerCase().includes(part.toLowerCase())
                                    );
                                  });
                                }

                                // If still no match, try to find the most recent minutes file
                                if (!matchingFile) {
                                  const minutesFiles = files.filter(file => file.endsWith('_minutes.md'));
                                  if (minutesFiles.length > 0) {
                                    // Sort by timestamp (assuming filename starts with timestamp)
                                    minutesFiles.sort((a, b) => {
                                      const timestampA = parseInt(a.split('-')[0]);
                                      const timestampB = parseInt(b.split('-')[0]);
                                      return timestampB - timestampA; // Descending order
                                    });
                                    matchingFile = minutesFiles[0]; // Most recent file
                                  }
                                }
                              }

                              if (matchingFile) {
                                console.log('Found matching file in main minutes directory:', matchingFile);

                                // Try to fetch the file content
                                const mainDirPath = `python/minutes of the meeting/${matchingFile}`;
                                return fetchFileContent(mainDirPath, {
                                  baseUrl: 'http://localhost:3001/api',
                                  endpoint: 'file'
                                })
                                  .then(minutesText => {
                                    console.log('Successfully fetched minutes from main directory');
                                    setMinutesContent(minutesText);

                                    // Update localStorage with the valid path
                                    transcriptData.minutes_file = mainDirPath;
                                    localStorage.setItem('currentTranscript', JSON.stringify(transcriptData));
                                  })
                                  .catch(mainDirErr => {
                                    console.error('Failed to fetch from main directory:', mainDirErr);
                                    setMinutesContent('Error loading minutes: ' + err.message + '\n\nTry uploading your meeting recording again to generate new minutes.');
                                  });
                              } else {
                                console.error('No matching file found in main minutes directory');
                                setMinutesContent('Error loading minutes: ' + err.message + '\n\nTry uploading your meeting recording again to generate new minutes.');
                              }
                            })
                            .catch(listErr => {
                              console.error('Error listing files in main minutes directory:', listErr);
                              setMinutesContent('Error loading minutes: ' + err.message + '\n\nTry uploading your meeting recording again to generate new minutes.');
                            });
                        });
                    }
                  })
                  .finally(() => {
                    setLoading(false); // End loading state regardless of success/failure
                  });
              } else {
                console.error('No minutes file path available:', transcriptData.minutes_file);
                setMinutesContent('Error loading minutes: No valid file path found in minute data\n\nTry uploading your meeting recording again to generate new minutes.');
                setLoading(false);
              }
            } else {
              console.error('No minutes file path available');
              setMinutesContent('No minutes file path available');
              setLoading(false); // Make sure loading is set to false
            }
          } else {
            // Use the minutes content directly from localStorage
            setMinutesContent(transcriptData.minutes);

            // Ensure we have attendance data for this transcript
            ensureAttendanceData();

            setLoading(false); // Make sure loading is set to false
          }
        } else {
          console.log('No minutes content found in transcriptData');
          console.log('Available fields in transcriptData:', Object.keys(transcriptData));

          // Try to fetch minutes from the database using the minute_id
          if (transcriptData.minute_id) {
            console.log('Attempting to fetch minutes from database with ID:', transcriptData.minute_id);

            // Fetch the minutes content from the server
            const userId = localStorage.getItem('userId');
            if (userId) {
              setLoading(true); // Set loading state while fetching
              fetch(`http://localhost:3001/api/minutes/${userId}/${transcriptData.minute_id}`, {
                credentials: 'include',
                headers: {
                  'Accept': 'application/json',
                }
              })
              .then(response => response.json())
              .then(data => {
                console.log('Fetched minute data from server:', data);
                if (data.status === 'success' && data.minute) {
                  console.log('Minute data from server:', data.minute);

                  // Check if we have any file path
                  if (data.minute.file_path &&
                      typeof data.minute.file_path === 'string' &&
                      data.minute.file_path.trim() !== '') {

                    console.log('Found file path in minute data:', data.minute.file_path);

                    // Normalize the path - ensure it has proper slashes
                    let normalizedPath = data.minute.file_path.trim();

                    // If path doesn't have slashes, try to construct a valid path
                    if (!normalizedPath.includes('/') && !normalizedPath.includes('\\')) {
                      console.log('Path does not contain slashes, attempting to normalize:', normalizedPath);

                      // Try to extract directory from the path if it looks like a filename
                      const parts = normalizedPath.split('.');
                      if (parts.length > 1) {
                        // Assume it's a filename, add a default path
                        normalizedPath = `uploads/processed/${data.minute.id || 'latest'}/minutes of the meeting/${normalizedPath}`;
                        console.log('Constructed path with default directory:', normalizedPath);
                      }
                    }

                    // Try to read the minutes file
                    return fetchFileContent(normalizedPath, {
                      baseUrl: 'http://localhost:3001/api',
                      endpoint: 'file'
                    })
                      .then(minutesText => {
                        console.log('Fetched minutes content from file, length:', minutesText.length);
                        if (minutesText.trim() === '') {
                          throw new Error('Minutes file is empty');
                        }
                        setMinutesContent(minutesText);

                        // Update localStorage with the actual minutes content
                        transcriptData.minutes = minutesText;
                        transcriptData.minutes_file = normalizedPath; // Update the file path in localStorage
                        localStorage.setItem('currentTranscript', JSON.stringify(transcriptData));
                      });
                  } else {
                    console.error('No file path in minute data:', data.minute.file_path);
                    throw new Error('No file path found in minute data. Try uploading your meeting recording again.');
                  }
                } else {
                  console.error('Invalid minute data from server:', data);
                  throw new Error('No valid minute data found');
                }
              })
              .catch(err => {
                console.error('Error fetching minute details:', err);

                // Try to find the file in the main minutes directory as a fallback
                console.log('Trying to find minutes file in main minutes directory');

                // Try to find the file in the main minutes directory
                fetch(`http://localhost:3001/api/list-files?path=python/minutes of the meeting`)
                  .then(response => response.json())
                  .then(files => {
                    console.log('Files in main minutes directory:', files);

                    // Look for a file that contains the meeting title
                    let matchingFile = null;

                    if (files && Array.isArray(files) && title) {
                      const titleParts = title.split(' ');
                      matchingFile = files.find(file => {
                        // Check if the file contains any part of the title
                        return titleParts.some(part =>
                          part.length > 3 && file.toLowerCase().includes(part.toLowerCase())
                        );
                      });

                      // If no match by title, try to find the most recent minutes file
                      if (!matchingFile) {
                        const minutesFiles = files.filter(file => file.endsWith('_minutes.md'));
                        if (minutesFiles.length > 0) {
                          // Sort by timestamp (assuming filename starts with timestamp)
                          minutesFiles.sort((a, b) => {
                            const timestampA = parseInt(a.split('-')[0]);
                            const timestampB = parseInt(b.split('-')[0]);
                            return timestampB - timestampA; // Descending order
                          });
                          matchingFile = minutesFiles[0]; // Most recent file
                        }
                      }
                    }

                    if (matchingFile) {
                      console.log('Found matching file in main minutes directory:', matchingFile);

                      // Try to fetch the file content
                      const mainDirPath = `python/minutes of the meeting/${matchingFile}`;
                      return fetchFileContent(mainDirPath, {
                        baseUrl: 'http://localhost:3001/api',
                        endpoint: 'file'
                      })
                        .then(minutesText => {
                          console.log('Successfully fetched minutes from main directory');
                          setMinutesContent(minutesText);

                          // Update localStorage with the valid path
                          if (transcriptData) {
                            transcriptData.minutes_file = mainDirPath;
                            localStorage.setItem('currentTranscript', JSON.stringify(transcriptData));
                          }
                        })
                        .catch(mainDirErr => {
                          console.error('Failed to fetch from main directory:', mainDirErr);
                          setMinutesContent('Error loading minutes: ' + err.message);
                        });
                    } else {
                      console.error('No matching file found in main minutes directory');
                      setMinutesContent('Error loading minutes: ' + err.message);
                    }
                  })
                  .catch(listErr => {
                    console.error('Error listing files in main minutes directory:', listErr);
                    setMinutesContent('Error loading minutes: ' + err.message);
                  });
              })
              .finally(() => {
                setLoading(false); // End loading state regardless of success/failure
              });
            }
          }
        }

        // Set minutes file path if available
        if (transcriptData.minutes_file) {
          setMinutesFile(transcriptData.minutes_file);
        }

        // Set title if available
        if (transcriptData.title) {
          setTitle(transcriptData.title);
        }



        // Set attendance data if available
        console.log('Minutes: Processing attendance data');

        // Try to get attendance data from our attendance service first
        let attendanceData = [];
        if (transcriptData.title) {
          console.log('Minutes: Getting attendance data for meeting:', transcriptData.title);
          attendanceData = getAttendanceForMeeting(transcriptData.title, transcriptData.id);
        }

        // If we found attendance data from the service, use it
        if (attendanceData && attendanceData.length > 0) {
          console.log('Minutes: Found attendance data from service:', attendanceData.length, 'records');

          // Process the attendance data to ensure it has all required fields
          const processedAttendance = processAttendanceData(attendanceData);
          setAttendanceData(processedAttendance);

          // Extract names of present attendees for the basic minuteData
          const attendeeNames = processedAttendance
            .filter(person => person.status === 'present')
            .map(person => person.name);

          if (attendeeNames.length > 0) {
            setMinuteData(prev => ({
              ...prev,
              attendees: attendeeNames
            }));
          }
        }
        // Fallback to transcript attendance data if available
        else if (transcriptData.attendance && Array.isArray(transcriptData.attendance)) {
          console.log('Minutes: Using attendance data from transcript:', transcriptData.attendance.length, 'records');

          // Process the attendance data to ensure it has all required fields
          const processedAttendance = processAttendanceData(transcriptData.attendance);
          setAttendanceData(processedAttendance);

          // Extract names of present attendees for the basic minuteData
          const attendeeNames = processedAttendance
            .filter(person => person.status === 'present')
            .map(person => person.name);

          if (attendeeNames.length > 0) {
            setMinuteData(prev => ({
              ...prev,
              attendees: attendeeNames
            }));
          }
        } else {
          console.log('Minutes: No attendance data found, creating default attendance data');

          // Create default attendance data with all faculty present
          const defaultAttendance = createDefaultAttendanceData();

          // Save this default attendance for the current meeting
          if (transcriptData.title) {
            console.log('Minutes: Saving default attendance for meeting:', transcriptData.title);
            saveAttendanceForMeeting(transcriptData.title, defaultAttendance, transcriptData.id);
          }

          // Set the attendance data in the component state
          setAttendanceData(defaultAttendance);

          // Extract names of present attendees for the basic minuteData
          const attendeeNames = defaultAttendance
            .filter(person => person.status === 'present')
            .map(person => person.name);

          if (attendeeNames.length > 0) {
            setMinuteData(prev => ({
              ...prev,
              attendees: attendeeNames
            }));
          }
        }

        // Set date/time if available
        if (transcriptData.timestamp) {
          try {
            const date = new Date(transcriptData.timestamp);
            setMinuteData(prev => ({
              ...prev,
              dateTime: date.toLocaleString()
            }));
          } catch (e) {
            console.error('Error parsing timestamp:', e);
          }
        }

        // Set meeting date if available
        if (transcriptData.meetingDate) {
          try {
            const meetingDate = new Date(transcriptData.meetingDate);
            setMinuteData(prev => ({
              ...prev,
              meetingDate: meetingDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })
            }));
          } catch (e) {
            console.error('Error parsing meeting date:', e);
          }
        }
      } catch (e) {
        console.error('Error parsing stored transcript:', e);
      }
    } else {
      console.log('No transcript data found in localStorage');
      setLoading(false);
    }

    // Call ensureAttendanceData after a short delay to make sure title is set
    setTimeout(() => {
      ensureAttendanceData();
    }, 500);

  }, [id, title]); // Add id and title to the dependency array so the effect runs when they change

  const handleBackClick = () => {
    navigate(-1);
  };

  const generateDocx = async () => {
    try {
      console.log("Starting DOCX generation...");
      setLoading(true);

      // Try to use the server-side template-based DOCX generation
      // Prepare the data for the server
      // Apply the same filtering to the content that we use for display
      const filteredContent = filterKeyParticipants(minutesContent || 'No minutes content available');

      // We'll send the opening statement separately to avoid duplication
      // The Python script will handle adding it to the document
      const combinedContent = filteredContent;

      const minutesData = {
        title: title,
        dateTime: minuteData.dateTime || new Date().toLocaleString(),
        meetingDate: minuteData.meetingDate || new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        attendees: attendanceData.length > 0
          ? attendanceData
              .filter(person => person.status === 'present')
              .map(person => person.name)
          : minuteData.attendees || [],
        content: combinedContent,
        actionItems: minuteData.actionItems || [],
        openingStatement: minuteData.openingStatement || "",
        facilitator: minuteData.facilitator || "",
        meetingTime: minuteData.meetingTime || "",
        meetingPlatform: minuteData.meetingPlatform || ""
      };

      console.log("Sending data to server for DOCX generation:", minutesData);

      // Make a request to the server to generate the DOCX
      fetch('http://localhost:3001/api/generate-docx', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(minutesData),
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        return response.blob();
      })
      .then(blob => {
        console.log("Received DOCX blob from server");

        // Create a download link and trigger the download
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_minutes.docx`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log("DOCX download initiated");
        setLoading(false);
      })
      .catch(error => {
        console.error("Error with server-side DOCX generation:", error);
        console.log("Falling back to client-side text generation");

        // Fallback to text file generation
        generateTextFile();
      });
    } catch (error) {
      console.error("Error generating document:", error);
      alert("Error generating document. Please try again.");
      setLoading(false);
    }
  };

  // Function to generate a text file
  const generateTextFile = () => {
    try {
      console.log("Starting text file generation...");

      // Create a text content for the document with improved formatting
      let content = '';

      // Add title with some decoration
      content += `===========================================\n`;
      content += `${title.toUpperCase()}\n`;
      content += `===========================================\n\n`;

      // Add meeting date and creation time
      content += `Date of Meeting: ${minuteData.meetingDate || new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })}\n`;
      content += `Created At: ${minuteData.dateTime || new Date().toLocaleString()}\n\n`;

      // Add attendees with better formatting
      content += `Present Faculty Attendees:\n`;
      content += `-------------------\n`;

      const attendeesList = attendanceData.length > 0
        ? attendanceData
            .filter(person => person.status === 'present')
            .map(person => person.name)
        : minuteData.attendees || [];

      if (attendeesList && attendeesList.length > 0) {
        attendeesList.forEach(attendee => {
          content += `- ${attendee}\n`;
        });
      } else {
        content += `No attendance data available\n`;
      }

      // Add agenda if available
      if (minuteData.agenda && minuteData.agenda.trim() !== '') {
        content += `\nAgenda:\n`;
        content += `-------------------\n`;
        content += `${minuteData.agenda}\n`;
      }

      // Add action items if available
      const actionItems = minuteData.actionItems || [];
      if (actionItems.length > 0) {
        content += `\nAction Items:\n`;
        content += `-------------------\n`;
        actionItems.forEach((item, index) => {
          content += `${index + 1}. ${item}\n`;
        });
      }

      // Add opening statement if available
      if (minuteData.openingStatement) {
        content += `\n===========================================\n`;
        content += `Opening Statement:\n`;
        content += `===========================================\n\n`;
        content += `${minuteData.openingStatement}\n\n`;
      }

      // Add minutes content with better section formatting
      content += `\n===========================================\n`;
      content += `Generated Minutes of the Meeting:\n`;
      content += `===========================================\n\n`;

      // Format the minutes content to preserve markdown formatting and filter out key participants
      const formattedMinutes = filterKeyParticipants(minutesContent || 'No minutes content available');
      content += formattedMinutes;

      console.log("Text content prepared with improved formatting");

      // Create a blob with the text content
      const blob = new Blob([content], { type: 'text/plain' });

      // Create a download link and trigger the download
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_minutes.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log("Text file download initiated");
      setLoading(false);
    } catch (error) {
      console.error("Error generating text file:", error);
      alert("Error generating document. Please try again.");
      setLoading(false);
    }
  };

  // Apply no-scrollbar class to document when component mounts
  useEffect(() => {
    // Add classes to hide scrollbars
    document.documentElement.classList.add('no-scrollbar');
    document.body.classList.add('no-scrollbar');

    // Force scrollbars to be hidden with inline styles as well
    document.documentElement.style.overflow = 'hidden';
    document.documentElement.style.msOverflowStyle = 'none';
    document.documentElement.style.scrollbarWidth = 'none';

    document.body.style.overflow = 'hidden';
    document.body.style.msOverflowStyle = 'none';
    document.body.style.scrollbarWidth = 'none';

    // Cleanup when component unmounts
    return () => {
      document.documentElement.classList.remove('no-scrollbar');
      document.body.classList.remove('no-scrollbar');

      // Reset inline styles
      document.documentElement.style.overflow = '';
      document.documentElement.style.msOverflowStyle = '';
      document.documentElement.style.scrollbarWidth = '';

      document.body.style.overflow = '';
      document.body.style.msOverflowStyle = '';
      document.body.style.scrollbarWidth = '';
    };
  }, []);

  return (
    <div className="minutes-container" style={{
      display: 'flex',
      minHeight: '100vh',
      height: 'auto',
      fontFamily: 'Montserrat, sans-serif',
      position: 'relative',
      overflow: 'visible',
      msOverflowStyle: 'none',
      scrollbarWidth: 'none'
    }}>
      {/* Background Pattern */}
      <div className="background-pattern" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          linear-gradient(45deg, rgba(3, 79, 175, 0.1), rgba(87, 215, 226, 0.1)),
          url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23034FAF' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
        `,
        animation: 'backgroundShift 30s linear infinite',
        zIndex: 0,
      }} />

      {/* Gradient Overlay */}
      <div className="gradient-overlay" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(3, 79, 175, 0.25) 100%)',
        zIndex: 1,
      }} />

      {/* Decorative Elements */}
      <div className="decorative-circle circle-1" style={{
        position: 'absolute',
        width: '400px',
        height: '400px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(3, 79, 175, 0.25) 0%, rgba(87, 215, 226, 0.12) 70%)',
        top: '-150px',
        right: '5%',
        animation: 'float 15s ease-in-out infinite, shimmer 8s infinite',
        zIndex: 1,
        opacity: 0.7,
        filter: 'blur(80px)',
      }} />

      <div className="decorative-circle circle-2" style={{
        position: 'absolute',
        width: '300px',
        height: '300px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(87, 215, 226, 0.25) 0%, rgba(3, 79, 175, 0.12) 70%)',
        bottom: '5%',
        left: '25%',
        animation: 'float 12s ease-in-out infinite reverse, shimmer 10s infinite 2s',
        zIndex: 1,
        opacity: 0.7,
        filter: 'blur(80px)',
      }} />

      <Sidebar style={{ position: 'relative', zIndex: 2 }} />

      {/* Main Content */}
      <div className="content-area" style={{
        flex: 1,
        padding: '30px',
        minHeight: '100vh',
        height: 'auto',
        overflow: 'auto',
        position: 'relative',
        zIndex: 2,
        msOverflowStyle: 'none',
        scrollbarWidth: 'none',
        WebkitOverflowScrolling: 'touch',
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '30px',
          gap: '20px',
          background: 'rgba(255, 255, 255, 0.15)',
          padding: '15px 25px',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.25)',
          transition: 'all 0.3s ease',
          animation: 'fadeIn 0.8s ease-out',
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '12px',
            borderRadius: '50%',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.05)';
            e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.background = 'transparent';
          }}
          onClick={handleBackClick}
          >
            <img
              src={BackIcon}
              alt="back"
              style={{
                width: '28px',
                height: '28px',
                transition: 'transform 0.3s ease',
                cursor: 'pointer',
              }}
            />
          </div>
          <h1 style={{
            margin: 0,
            fontSize: "24px",
            fontWeight: "900",
            color: '#034FAF',
            letterSpacing: '0.5px',
            cursor: 'default',
            textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
          }}>{title.toUpperCase()}</h1>
        </div>

        {/* Content Section */}
        {loading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '200px',
            background: 'rgba(255, 255, 255, 0.15)',
            padding: '30px',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.25)',
            marginBottom: '30px',
          }}>
            <div style={{ fontSize: '18px', color: '#034FAF' }}>Loading minutes...</div>
          </div>
        ) : (
          <div style={{
            background: 'rgba(255, 255, 255, 0.15)',
            padding: '30px',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.25)',
            marginBottom: '30px',
          }}>
          <div style={{ marginBottom: '30px' }}>
            <h2 style={{
              color: '#034FAF',
              marginBottom: '15px',
              fontSize: '20px',
              fontWeight: '700',
              letterSpacing: '0.5px',
              cursor: 'default',
              textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
            }}>Meeting Information</h2>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '15px',
              background: 'rgba(255, 255, 255, 0.5)',
              borderRadius: '8px',
              backdropFilter: 'blur(5px)',
              WebkitBackdropFilter: 'blur(5px)',
              border: '1px solid rgba(255, 255, 255, 0.3)',
              padding: '15px',
            }}>
              <div>
                <h3 style={{
                  fontSize: '16px',
                  fontWeight: '600',
                  color: '#034FAF',
                  margin: '0 0 8px 0',
                }}>Date of Meeting</h3>
                <p style={{
                  fontSize: '16px',
                  color: '#333',
                  margin: '0',
                  padding: '8px 12px',
                  background: 'rgba(255, 255, 255, 0.5)',
                  borderRadius: '6px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  cursor: 'default',
                }}>{minuteData.meetingDate}</p>
              </div>
            </div>
          </div>



          {/* Opening Statement is now included in the minutes content */}

          <div style={{ marginBottom: '30px' }}>
            <h2 style={{
              color: '#034FAF',
              marginBottom: '15px',
              fontSize: '20px',
              fontWeight: '700',
              letterSpacing: '0.5px',
              cursor: 'default',
              textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
            }}>Present Faculty Attendees</h2>

            <div style={{
                background: 'rgba(255, 255, 255, 0.5)',
                borderRadius: '8px',
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                padding: '15px 20px',
                minHeight: 'fit-content',
                height: 'auto',
                overflow: 'visible',
              }}>
                {attendanceData.length > 0 && attendanceData.filter(person => person.status === 'present').length > 0 ? (
                  <ul style={{
                    paddingLeft: '20px',
                    margin: '0',
                    maxHeight: 'none',
                    overflow: 'visible',
                  }}>
                    {attendanceData
                      .filter(person => person.status === 'present')
                      .map((person, index) => (
                        <li key={index} style={{
                          fontSize: '16px',
                          color: '#333',
                          marginBottom: index === attendanceData.filter(p => p.status === 'present').length - 1 ? '0' : '12px',
                          lineHeight: '1.6',
                          cursor: 'default',
                        }}>
                          {person.name}
                        </li>
                      ))
                    }
                  </ul>
                ) : minuteData.attendees && minuteData.attendees.length > 0 ? (
                  <ul style={{
                    paddingLeft: '20px',
                    margin: '0',
                    maxHeight: 'none',
                    overflow: 'visible',
                  }}>
                    {minuteData.attendees.map((attendee, index) => (
                      <li key={index} style={{
                        fontSize: '16px',
                        color: '#333',
                        marginBottom: index === minuteData.attendees.length - 1 ? '0' : '12px',
                        lineHeight: '1.6',
                        cursor: 'default',
                      }}>
                        {attendee}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p style={{
                    fontSize: '16px',
                    color: '#666',
                    fontStyle: 'italic',
                    margin: '0',
                  }}>No attendance data available</p>
                )}
              </div>
          </div>
          {/* Processed Minutes Content */}
          <div style={{ marginBottom: '30px' }}>
            <h2 style={{
              color: '#034FAF',
              marginBottom: '15px',
              fontSize: '20px',
              fontWeight: '700',
              letterSpacing: '0.5px',
              cursor: 'default',
              textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
            }}>Generated Minutes of the Meeting</h2>

            {loading ? (
              <div style={{
                fontSize: '16px',
                color: '#666',
                margin: '0',
                padding: '20px',
                background: 'rgba(255, 255, 255, 0.5)',
                borderRadius: '8px',
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                lineHeight: '1.6',
                cursor: 'default',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}>
                <div>Loading minutes content...</div>
              </div>
            ) : minutesContent ? (
              <div className="minutes-content" style={{
                fontSize: '16px',
                color: '#333',
                margin: '0',
                padding: '20px',
                background: 'rgba(255, 255, 255, 0.5)',
                borderRadius: '8px',
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                lineHeight: '1.6',
                cursor: 'default',
                textShadow: '0 1px 2px rgba(255, 255, 255, 0.7)',
                maxHeight: '400px',
                overflowY: 'auto',
                msOverflowStyle: 'none',
                scrollbarWidth: 'none',
                WebkitOverflowScrolling: 'touch',
              }}>
                {minutesContent.startsWith('Error loading minutes:') ? (
                  <div style={{ color: '#d32f2f', fontWeight: '500' }}>
                    {minutesContent}
                    <div style={{ marginTop: '15px', fontStyle: 'italic' }}>
                      Try uploading your meeting recording again to generate new minutes.
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Debugging information removed as requested */}

                    {/* Try to render with ReactMarkdown first */}
                    <div style={{ fontFamily: 'Calibri, sans-serif', fontSize: '11pt', fontStyle: 'italic' }}>
                      {/* Opening statement (same font as the rest of the content) */}
                      <p>
                        {minuteData.openingStatement}
                      </p>

                      {/* Minutes content */}
                      <ReactMarkdown>
                        {filterKeyParticipants(minutesContent)}
                      </ReactMarkdown>
                    </div>

                    {/* Fallback to plain text if content is very short or seems problematic */}
                    {minutesContent.length < 50 && (
                      <div style={{ marginTop: '20px', padding: '10px', background: 'rgba(255, 0, 0, 0.05)', borderRadius: '4px' }}>
                        <strong>Raw Content (Fallback):</strong>
                        <pre style={{ whiteSpace: 'pre-wrap', fontFamily: 'Calibri, sans-serif', fontSize: '11pt', fontStyle: 'italic' }}>
                          {minutesContent}
                        </pre>
                      </div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <div style={{
                fontSize: '16px',
                color: '#666',
                fontStyle: 'italic',
                margin: '0',
                padding: '20px',
                background: 'rgba(255, 255, 255, 0.5)',
                borderRadius: '8px',
                backdropFilter: 'blur(5px)',
                WebkitBackdropFilter: 'blur(5px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                lineHeight: '1.6',
                cursor: 'default',
              }}>
                No processed minutes available. Please upload a meeting recording to generate minutes.
              </div>
            )}

            {/* File path display removed as requested */}
          </div>

          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: '20px',
          }}>
            <button
              onClick={generateDocx}
              style={{
                padding: '12px 24px',
                background: 'linear-gradient(45deg, #034FAF, #57D7E2)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                fontSize: '16px',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
              }}
              onMouseOver={(e) => {
                e.target.style.background = 'linear-gradient(45deg, #0347A0, #4BC0CB)';
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 6px 8px rgba(0, 0, 0, 0.15)';
              }}
              onMouseOut={(e) => {
                e.target.style.background = 'linear-gradient(45deg, #034FAF, #57D7E2)';
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
              }}
            >
              {loading ? "Generating..." : "Download as DOCX"}
            </button>
          </div>
        </div>
        )}

        <style>
          {`
            @keyframes backgroundShift {
              0% { background-position: 0 0; }
              100% { background-position: 100% 100%; }
            }

            @keyframes float {
              0% { transform: translateY(0) rotate(0deg); }
              50% { transform: translateY(-15px) rotate(5deg); }
              100% { transform: translateY(0) rotate(0deg); }
            }

            @keyframes shimmer {
              0% { opacity: 0.7; }
              50% { opacity: 0.9; }
              100% { opacity: 0.7; }
            }

            @keyframes fadeIn {
              from { opacity: 0; transform: translateY(10px); }
              to { opacity: 1; transform: translateY(0); }
            }

            /* Hide scrollbar for Chrome, Safari and Opera */
            *::-webkit-scrollbar {
              display: none !important;
              width: 0 !important;
              height: 0 !important;
              background: transparent !important;
            }

            /* Hide scrollbar for IE, Edge and Firefox */
            * {
              -ms-overflow-style: none !important;  /* IE and Edge */
              scrollbar-width: none !important;  /* Firefox */
            }

            /* Ensure content areas with scrolling don't show scrollbars */
            .content-area, .minutes-content, .minutes-container {
              -ms-overflow-style: none !important;
              scrollbar-width: none !important;
            }

            .content-area::-webkit-scrollbar,
            .minutes-content::-webkit-scrollbar,
            .minutes-container::-webkit-scrollbar {
              display: none !important;
              width: 0 !important;
              height: 0 !important;
            }

            /* Default cursor for text elements */
            h1, h2, h3, h4, h5, h6, p, span, div, section, label, th {
              cursor: default !important;
            }

            /* Ensure buttons and interactive elements have pointer cursor */
            button, a, .clickable, [role="button"], input[type="file"], input[type="submit"] {
              cursor: pointer !important;
            }

            /* Make table rows with pointer cursor */
            tr[onClick] {
              cursor: pointer !important;
            }
          `}
        </style>


      </div>
    </div>
  );
};

export default Minutes;
