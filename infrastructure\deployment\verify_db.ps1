# PowerShell script to verify the database setup

# Get the current script directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to the project directory
Set-Location -Path $scriptPath

Write-Host "Running database verification script..." -ForegroundColor Green
Write-Host ""

# Run the verification script
try {
    node verify_database.js
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
