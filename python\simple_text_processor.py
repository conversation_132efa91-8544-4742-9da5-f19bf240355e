#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple Text File Processing Script for Meeting Assistant

This script processes text files (transcripts) directly, bypassing audio processing
and only performing translation if needed before proceeding to generate simple minutes.
"""

import os
import sys
import json
import time
import argparse
import traceback

# Try to import deep_translator
try:
    from deep_translator import GoogleTranslator
    print("Successfully imported GoogleTranslator from deep_translator")
except ImportError:
    print("Installing deep_translator...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "deep_translator"])
    from deep_translator import GoogleTranslator
    print("Successfully installed and imported GoogleTranslator")

# Try to import Phase 2 for minutes generation
try:
    # Try with correct directory name (with space)
    print("Trying to import Phase2V2 from 'Pipeline Phase2' directory...")
    import sys
    import os

    # Add the current directory to sys.path to enable imports
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.append(current_dir)

    # The parent directory is the project root
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.append(parent_dir)

    # Python doesn't allow spaces in module names, so we'll skip this approach
    # and go directly to the file-based import in the next try block
    raise ImportError("Skipping direct import with spaces in module name")
except (ImportError, SyntaxError) as e:
    print(f"First Phase2 import attempt failed: {e}")
    try:
        # Try with direct file path
        print("Trying to import Phase2V2 with direct file path...")
        import importlib.util

        # Get the absolute path to the Phase2V2.py file
        # Use raw string to handle backslashes in Windows paths
        phase2_path = os.path.join(current_dir, r"Pipeline Phase2", "Phase2V2.py")
        print(f"Looking for Phase2V2.py at: {phase2_path}")

        if os.path.exists(phase2_path):
            # Load the module from the file path
            spec = importlib.util.spec_from_file_location("Phase2V2", phase2_path)
            phase2_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(phase2_module)

            # Get the function from the module
            phase2v2_process = phase2_module.process_meeting_transcript_enhanced
            print(f"Successfully imported Phase2V2 from file: {phase2_path}")
            USE_FULL_PHASE2 = True
        else:
            # Try with PHASE2.py instead
            phase2_path = os.path.join(current_dir, r"Pipeline Phase2", "PHASE2.py")
            print(f"Looking for PHASE2.py at: {phase2_path}")
            if os.path.exists(phase2_path):
                # Load the module from the file path
                spec = importlib.util.spec_from_file_location("PHASE2", phase2_path)
                phase2_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(phase2_module)

                # Get the function from the module
                phase2v2_process = phase2_module.process_meeting_transcript_enhanced
                print(f"Successfully imported PHASE2 from file: {phase2_path}")
                USE_FULL_PHASE2 = True
            else:
                raise ImportError(f"Could not find Phase2V2.py or PHASE2.py in 'Pipeline Phase2' directory")
    except Exception as e2:
        print(f"All Phase2 import attempts failed: {e2}")
        print("Will use simplified minutes generation")
        USE_FULL_PHASE2 = False

def read_text_file(file_path):
    """Read text from a file and remove BOM character if present."""
    try:
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            # utf-8-sig automatically removes the BOM character
            return f.read()
    except UnicodeDecodeError:
        # Try with different encodings if UTF-8 fails
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                text = f.read()
                # Remove BOM if present
                if text.startswith('\ufeff'):
                    text = text[1:]
                return text
        except Exception as e:
            print(f"Error reading file with latin-1 encoding: {e}")
            raise

def translate_text(text, source_lang='tl', target_lang='en'):
    """Translate text from source language to target language."""
    translator = GoogleTranslator(source=source_lang, target=target_lang)

    # Split text into sentences to better handle long texts
    import re
    sentences = re.split(r'([.!?]\s+)', text)

    # Recombine into sentence pairs (sentence + punctuation)
    sentence_pairs = []
    for i in range(0, len(sentences)-1, 2):
        if i+1 < len(sentences):
            sentence_pairs.append(sentences[i] + sentences[i+1])
        else:
            sentence_pairs.append(sentences[i])

    # If there's an odd number of items, add the last one
    if len(sentences) % 2 == 1:
        sentence_pairs.append(sentences[-1])

    # Group sentences into chunks of max 4000 characters (well below the 5000 limit)
    chunks = []
    current_chunk = ""

    for sentence in sentence_pairs:
        # If adding this sentence would exceed the limit, start a new chunk
        if len(current_chunk) + len(sentence) > 4000:
            chunks.append(current_chunk)
            current_chunk = sentence
        else:
            current_chunk += sentence

    # Add the last chunk if it's not empty
    if current_chunk:
        chunks.append(current_chunk)

    print(f"Split text into {len(chunks)} chunks for translation")

    # Translate each chunk
    translated_chunks = []
    for i, chunk in enumerate(chunks):
        try:
            print(f"Translating chunk {i+1}/{len(chunks)} ({len(chunk)} characters)")
            # Skip empty chunks
            if not chunk.strip():
                translated_chunks.append("")
                continue

            # Handle very short chunks directly
            translated = translator.translate(chunk)
            translated_chunks.append(translated)

        except Exception as e:
            print(f"Error translating chunk {i+1}: {e}")
            # If translation fails, keep the original text
            translated_chunks.append(chunk)

    return ''.join(translated_chunks)

def generate_simple_minutes(transcript):
    """Generate simple minutes from a transcript."""
    # Extract speakers and their lines
    speakers = set()
    lines = transcript.split('\n')
    for line in lines:
        if ':' in line:
            speaker = line.split(':', 1)[0].strip()
            if speaker:
                speakers.add(speaker)

    # Generate a simple summary
    summary = f"# Meeting Minutes\n\n"

    # Add executive summary
    summary += "## Executive Summary\n\n"
    summary += "This is an automatically generated summary of the meeting transcript.\n\n"

    # Add participants
    if speakers:
        summary += "## Participants\n\n"
        for speaker in sorted(speakers):
            summary += f"- {speaker}\n"
        summary += "\n"

    # Add discussion topics (simplified)
    summary += "## Discussion Topics\n\n"
    summary += "The meeting covered the following topics:\n\n"

    # Extract some topics from the transcript (simplified approach)
    topics = []
    topic_count = 0

    for line in lines:
        if line.strip() and ':' in line:
            # Extract content after speaker
            content = line.split(':', 1)[1].strip()

            # Check if this might be a topic
            if len(content) > 20 and len(content) < 100 and content.endswith(('?', '.')):
                topics.append(content)
                topic_count += 1
                if topic_count >= 5:  # Limit to 5 topics
                    break

    # Add topics to summary
    if topics:
        for i, topic in enumerate(topics, 1):
            summary += f"{i}. {topic}\n"
    else:
        summary += "- General discussion\n"

    summary += "\n## Full Transcript\n\n"
    summary += "Please refer to the transcript file for the complete meeting content.\n"

    return summary

def process_text_file(input_file, output_dir, language='tagalog', title=None, faculty_attendance=None):
    """
    Process a text file (transcript) and generate minutes.

    Args:
        input_file (str): Path to the input text file
        output_dir (str): Directory to save output files
        language (str): Language of the input text ('tagalog' or 'english')
        title (str): Title for the meeting minutes
        faculty_attendance (str): JSON string containing faculty attendance data

    Returns:
        dict: Processing results
    """
    start_time = time.time()

    # Use only the main directories instead of job-specific output directories
    main_transcription_dir = 'C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription'
    main_minutes_dir = 'C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\minutes of the meeting'

    # Ensure the directories exist
    os.makedirs(main_transcription_dir, exist_ok=True)
    os.makedirs(main_minutes_dir, exist_ok=True)

    # Set the directories to use
    transcription_dir = main_transcription_dir
    minutes_dir = main_minutes_dir

    # Create the output_dir for compatibility with existing code, but we won't use it
    os.makedirs(output_dir, exist_ok=True)

    # Generate filenames
    timestamp = int(time.time() * 1000)
    base_filename = os.path.splitext(os.path.basename(input_file))[0]
    safe_filename = "".join([c if c.isalnum() or c in " ._-" else "_" for c in base_filename])

    if title:
        safe_title = "".join([c if c.isalnum() or c in " ._-" else "_" for c in title])
        output_base = f"{timestamp}-{safe_title}"
    else:
        output_base = f"{timestamp}-{safe_filename}"

    # Read the input file
    print("Reading text file")
    text_content = read_text_file(input_file)

    # Process based on language
    if language.lower() == 'tagalog':
        # Save original Tagalog transcript
        tagalog_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript_tagalog.txt")
        with open(tagalog_transcript_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"Saved Tagalog transcript to: {tagalog_transcript_path}")

        # Also save to the main transcription directory if it's different
        main_transcription_dir = 'C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription'
        if os.path.normpath(transcription_dir) != os.path.normpath(main_transcription_dir):
            try:
                # Ensure the directory exists
                os.makedirs(main_transcription_dir, exist_ok=True)

                # Save the Tagalog transcript to the main directory
                main_tagalog_path = os.path.join(main_transcription_dir, f"{output_base}_transcript_tagalog.txt")
                with open(main_tagalog_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                print(f"Also saved Tagalog transcript to main directory: {main_tagalog_path}")
            except Exception as e:
                print(f"Warning: Could not save to main transcription directory: {e}")

        print("Translating Tagalog text to English")

        # Translate to English
        try:
            # Check if text is too long and needs to be truncated
            if len(text_content) > 50000:  # Set a reasonable limit
                print(f"Text is very long ({len(text_content)} chars), truncating to 50000 chars for processing")
                truncated_text = text_content[:50000]

                # Save truncated text
                truncated_path = os.path.join(transcription_dir, f"{output_base}_truncated_tagalog.txt")
                with open(truncated_path, 'w', encoding='utf-8') as f:
                    f.write(truncated_text)

                # Use truncated text for translation
                text_to_translate = truncated_text
            else:
                text_to_translate = text_content

            # Translate the text
            print(f"Translating text ({len(text_to_translate)} chars)")
            english_text = translate_text(text_to_translate, source_lang='tl', target_lang='en')

            # Save translated English transcript
            english_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript.txt")
            with open(english_transcript_path, 'w', encoding='utf-8') as f:
                f.write(english_text)
            print(f"Saved English transcript to: {english_transcript_path}")

            # Also save to the main transcription directory if it's different
            if os.path.normpath(transcription_dir) != os.path.normpath(main_transcription_dir):
                try:
                    # Save the English transcript to the main directory
                    main_english_path = os.path.join(main_transcription_dir, f"{output_base}_transcript.txt")
                    with open(main_english_path, 'w', encoding='utf-8') as f:
                        f.write(english_text)
                    print(f"Also saved English transcript to main directory: {main_english_path}")
                except Exception as e:
                    print(f"Warning: Could not save English transcript to main directory: {e}")

            # Use the English text for minutes generation
            transcript_for_minutes = english_text
            transcript_path = english_transcript_path

        except Exception as e:
            # Handle Unicode errors safely
            try:
                print(f"Translation error: {str(e)}")
            except UnicodeEncodeError:
                print("Translation error: [Unicode error - cannot display]")

            print("Using original Tagalog text for minutes generation")
            transcript_for_minutes = text_content
            transcript_path = tagalog_transcript_path
    else:
        # Already in English, save directly
        print("Processing English text")
        english_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript.txt")
        with open(english_transcript_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"Saved English transcript to: {english_transcript_path}")

        # Also save as a Tagalog transcript file (same content) for frontend compatibility
        # This ensures the frontend can always find both files with the same naming pattern
        tagalog_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript_tagalog.txt")
        with open(tagalog_transcript_path, 'w', encoding='utf-8') as f:
            f.write(text_content)
        print(f"Also saved a copy as Tagalog transcript for compatibility: {tagalog_transcript_path}")

        # Also save to the main transcription directory if it's different
        main_transcription_dir = 'C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\meeting-assistantv2\\python\\transcription'
        if os.path.normpath(transcription_dir) != os.path.normpath(main_transcription_dir):
            try:
                # Ensure the directory exists
                os.makedirs(main_transcription_dir, exist_ok=True)

                # Save the English transcript to the main directory
                main_english_path = os.path.join(main_transcription_dir, f"{output_base}_transcript.txt")
                with open(main_english_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                print(f"Also saved English transcript to main directory: {main_english_path}")

                # Save the Tagalog copy to the main directory
                main_tagalog_path = os.path.join(main_transcription_dir, f"{output_base}_transcript_tagalog.txt")
                with open(main_tagalog_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                print(f"Also saved Tagalog copy to main directory: {main_tagalog_path}")
            except Exception as e:
                print(f"Warning: Could not save to main transcription directory: {e}")

        transcript_for_minutes = text_content
        transcript_path = english_transcript_path

    # Generate minutes
    print("Analyzing transcript content and generating minutes")

    try:
        # Use full Phase 2 processing if available, otherwise use simplified version
        if USE_FULL_PHASE2:
            print("Using full Phase 2 processing")
            try:
                minutes_content = phase2v2_process(transcript_for_minutes, include_executive_summary=True)
                print("Full Phase 2 processing successful")
            except Exception as phase2_error:
                print(f"Error in full Phase 2 processing: {phase2_error}")
                print("Falling back to simplified minutes generation")
                minutes_content = generate_simple_minutes(transcript_for_minutes)
        else:
            print("Using simplified minutes generation")
            minutes_content = generate_simple_minutes(transcript_for_minutes)

        # Save minutes with multiple naming patterns for better compatibility

        # 1. Save with timestamp-based pattern (primary filename)
        minutes_path = os.path.join(minutes_dir, f"{output_base}_minutes.md")
        with open(minutes_path, 'w', encoding='utf-8') as f:
            f.write(minutes_content)
        print(f"Saved minutes to: {minutes_path}")

        # 2. Save as minutes.md for compatibility with the frontend
        standard_minutes_path = os.path.join(minutes_dir, "minutes.md")
        with open(standard_minutes_path, 'w', encoding='utf-8') as f:
            f.write(minutes_content)
        print(f"Also saved minutes with standard name: {standard_minutes_path}")

        # 3. Save with alternative naming pattern
        alt_minutes_path = os.path.join(minutes_dir, f"{output_base}.md")
        with open(alt_minutes_path, 'w', encoding='utf-8') as f:
            f.write(minutes_content)
        print(f"Also saved minutes with alternative name: {alt_minutes_path}")

        print("Minutes generation complete")

    except Exception as e:
        print(f"Error generating minutes: {e}")
        traceback.print_exc()

        # Create a simple error message as minutes
        error_message = f"# Minutes Generation Failed\n\nThe system was unable to generate minutes due to the following error:\n\n```\n{str(e)}\n```\n\n## Transcript\n\nPlease refer to the transcript file for the meeting content."

        # Save error minutes with multiple naming patterns for better compatibility

        # 1. Save with timestamp-based pattern (primary filename)
        minutes_path = os.path.join(minutes_dir, f"{output_base}_minutes.md")
        with open(minutes_path, 'w', encoding='utf-8') as f:
            f.write(error_message)
        print(f"Saved error minutes to: {minutes_path}")

        # 2. Save as minutes.md for compatibility with the frontend
        standard_minutes_path = os.path.join(minutes_dir, "minutes.md")
        with open(standard_minutes_path, 'w', encoding='utf-8') as f:
            f.write(error_message)
        print(f"Also saved error minutes with standard name: {standard_minutes_path}")

        # 3. Save with alternative naming pattern
        alt_minutes_path = os.path.join(minutes_dir, f"{output_base}.md")
        with open(alt_minutes_path, 'w', encoding='utf-8') as f:
            f.write(error_message)
        print(f"Also saved error minutes with alternative name: {alt_minutes_path}")

        minutes_content = error_message

    # Calculate processing time
    processing_time = time.time() - start_time

    # Parse faculty attendance data if provided
    attendance_data = []
    if faculty_attendance:
        try:
            # Try to parse the faculty attendance JSON string
            import json
            attendance_data = json.loads(faculty_attendance)
            print(f"Parsed faculty attendance data: {len(attendance_data)} records")
        except Exception as e:
            print(f"Error parsing faculty attendance data: {e}")

    # Prepare result with all file paths for better frontend compatibility
    standard_minutes_path = os.path.join(minutes_dir, "minutes.md")
    alt_minutes_path = os.path.join(minutes_dir, f"{output_base}.md")

    # Create a job ID that will be used in file paths
    job_id = f"text_{timestamp}"

    # Prepare the result with comprehensive data for better frontend compatibility
    result = {
        "status": "success",
        "processing_time": processing_time,
        "processing_time_formatted": f"{processing_time:.2f} seconds",
        "result": {
            "data": {
                # File paths
                "transcript_file": transcript_path,
                "minutes_file": minutes_path,  # Primary minutes file with timestamp-based pattern
                "standard_minutes_file": standard_minutes_path,  # Standard minutes.md file
                "alt_minutes_file": alt_minutes_path,  # Alternative naming pattern
                "minutes_md_path": standard_minutes_path,

                # Content
                "transcript": transcript_for_minutes,  # English transcript content
                "minutes": minutes_content,

                # Metadata
                "speakers": [],  # No speaker detection for text files
                "transcription_dir": transcription_dir,
                "minutes_dir": minutes_dir,
                "job_id": job_id,
                "attendance": attendance_data,  # Include attendance data
                "title": title,  # Include title
                "timestamp": int(time.time() * 1000),  # Add timestamp

                # Flags
                "isTextFile": True,  # Flag to indicate this is a text file
                "textLanguage": language.lower(),  # Include the language
            }
        }
    }

    # Always include both original and translated content regardless of language
    if language.lower() == 'tagalog':
        # For Tagalog, the original is Tagalog and the translated is English
        result["result"]["data"]["original_transcript"] = text_content  # Tagalog content
        result["result"]["data"]["original_transcript_file"] = tagalog_transcript_path

        # Include normalized paths that the frontend might expect
        normalized_tagalog_path = f"python/transcription/{output_base}_transcript_tagalog.txt"
        result["result"]["data"]["original_transcript_path"] = normalized_tagalog_path
    else:
        # For English, we're using the same content for both but with different file paths
        # This ensures the frontend can always find both files with consistent naming patterns
        result["result"]["data"]["original_transcript"] = text_content  # Same content
        result["result"]["data"]["original_transcript_file"] = tagalog_transcript_path  # But use the Tagalog file path

        # Include normalized paths that the frontend might expect
        normalized_tagalog_path = f"python/transcription/{output_base}_transcript_tagalog.txt"
        result["result"]["data"]["original_transcript_path"] = normalized_tagalog_path

    return result

def main():
    print("Starting simple text file processing script...")

    try:
        parser = argparse.ArgumentParser(description="Process text files for meeting minutes")
        parser.add_argument("input_file", help="Path to the input text file")
        parser.add_argument("--output_dir", help="Directory to save output files", default=None)
        parser.add_argument("--language", help="Language of the input text (tagalog or english)", default="tagalog")
        parser.add_argument("--title", help="Title for the meeting minutes", default=None)
        parser.add_argument("--faculty_attendance", help="JSON string containing faculty attendance data", default=None)

        args = parser.parse_args()

        print(f"Arguments parsed: input_file={args.input_file}, language={args.language}, title={args.title}")
        if args.faculty_attendance:
            print(f"Faculty attendance data provided: {len(args.faculty_attendance)} characters")

        # Check if input file exists
        if not os.path.exists(args.input_file):
            print(f"Error: Input file not found: {args.input_file}")
            print(f"Current working directory: {os.getcwd()}")
            print(f"Directory contents: {os.listdir(os.path.dirname(args.input_file) or '.')}")
            raise FileNotFoundError(f"Input file not found: {args.input_file}")

        # Set default output directory if not provided
        if not args.output_dir:
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            args.output_dir = os.path.join(base_dir, "uploads", "processed", f"text_{int(time.time())}")
            print(f"Using default output directory: {args.output_dir}")

        try:
            print(f"Processing text file: {args.input_file}")
            result = process_text_file(
                args.input_file,
                args.output_dir,
                args.language,
                args.title,
                args.faculty_attendance
            )

            # Print result as JSON
            print("===JSON_OUTPUT_START===")
            print(json.dumps(result, indent=2))
            print("===JSON_OUTPUT_END===")

        except Exception as e:
            # Handle Unicode errors safely
            try:
                print(f"Error processing text file: {str(e)}")
            except UnicodeEncodeError:
                print("Error processing text file: [Unicode error - cannot display]")

            # Create a safe traceback string
            try:
                tb_str = traceback.format_exc()
                # Remove problematic Unicode characters
                safe_tb_str = ''.join(c if ord(c) < 128 else '?' for c in tb_str)
                print(safe_tb_str)
            except:
                print("Error generating traceback")

            # Create a safe error response
            error_msg = str(e)
            try:
                # Remove problematic Unicode characters
                safe_error_msg = ''.join(c if ord(c) < 128 else '?' for c in error_msg)
            except:
                safe_error_msg = "Unicode error in error message"

            print("===JSON_OUTPUT_START===")
            print(json.dumps({
                "status": "error",
                "error": safe_error_msg,
                "traceback": "Error occurred during processing"
            }, indent=2))
            print("===JSON_OUTPUT_END===")
            sys.exit(1)

    except Exception as e:
        # Handle Unicode errors safely
        try:
            print(f"Error in main function: {str(e)}")
        except UnicodeEncodeError:
            print("Error in main function: [Unicode error - cannot display]")

        # Create a safe traceback string
        try:
            tb_str = traceback.format_exc()
            # Remove problematic Unicode characters
            safe_tb_str = ''.join(c if ord(c) < 128 else '?' for c in tb_str)
            print(safe_tb_str)
        except:
            print("Error generating traceback")

        # Create a safe error response
        error_msg = str(e)
        try:
            # Remove problematic Unicode characters
            safe_error_msg = ''.join(c if ord(c) < 128 else '?' for c in error_msg)
        except:
            safe_error_msg = "Unicode error in error message"

        print("===JSON_OUTPUT_START===")
        print(json.dumps({
            "status": "error",
            "error": f"Error in main function: {safe_error_msg}",
            "traceback": "Error occurred during processing"
        }, indent=2))
        print("===JSON_OUTPUT_END===")
        sys.exit(1)

if __name__ == "__main__":
    main()
