"""
GPU and performance configuration for the meeting assistant.
This file contains settings to optimize processing speed based on available hardware.
"""

import os
import torch
import psutil
import gc

# Performance settings
ENABLE_GPU = True  # Set to False to force CPU usage
LOW_MEMORY_MODE = False  # Set to True for systems with limited RAM
PARALLEL_PROCESSING = True  # Enable parallel processing
MAX_WORKERS = None  # None = use all available CPU cores
CONSISTENT_DEVICE = True  # If True, stick with one device (GPU or CPU) throughout processing

# GPU settings
GPU_MEMORY_FRACTION = 0.6  # Reduced from 0.85 to avoid memory switching
BATCH_SIZE_GPU = 1  # Reduced batch size for more stable GPU processing
BATCH_SIZE_CPU = 1  # Batch size when using CPU
NUM_BEAMS_GPU = 2  # Reduced beam search size to save memory
NUM_BEAMS_CPU = 2  # Beam search size for CPU
GPU_MEMORY_THRESHOLD = 0.5  # Memory threshold for switching to CPU (0.0-1.0)

# Model precision
USE_HALF_PRECISION = True  # Use FP16 (half precision) on GPU to save memory

# Memory management
CLEAR_CACHE_FREQUENCY = 3  # Clear cache more frequently
AGGRESSIVE_MEMORY_CLEARING = True  # More aggressive memory clearing

# GTX 1050 Ti specific optimizations
GTX_1050TI_MODE = True  # Enable specific optimizations for GTX 1050 Ti

def get_system_info():
    """Get information about the system's hardware."""
    info = {
        "cpu_count": os.cpu_count(),
        "ram_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "gpu_available": torch.cuda.is_available(),
        "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
    }

    if info["gpu_available"]:
        info["gpu_name"] = torch.cuda.get_device_name(0)
        info["gpu_memory_gb"] = round(torch.cuda.get_device_properties(0).total_memory / (1024**3), 2)

    return info

def optimize_for_system():
    """Automatically optimize settings based on the system's hardware."""
    info = get_system_info()
    settings = {}

    # Determine if GPU should be used
    settings["use_gpu"] = info["gpu_available"] and ENABLE_GPU
    settings["consistent_device"] = CONSISTENT_DEVICE

    # Optimize for CPU count
    if info["cpu_count"] <= 4:
        settings["max_workers"] = max(2, info["cpu_count"] - 1)
    else:
        settings["max_workers"] = info["cpu_count"] - 2  # Leave some cores for system

    # Optimize for RAM
    if info["ram_gb"] < 8:
        settings["low_memory_mode"] = True
        settings["batch_size_cpu"] = 1
    else:
        settings["low_memory_mode"] = False
        settings["batch_size_cpu"] = min(2, max(1, int(info["ram_gb"] / 8)))

    # Check for GTX 1050 Ti specifically
    is_gtx_1050ti = False
    if settings["use_gpu"] and info["gpu_available"]:
        if "1050 Ti" in info["gpu_name"]:
            is_gtx_1050ti = True
            print("Detected GTX 1050 Ti - applying specialized optimizations")

    # Apply GTX 1050 Ti specific optimizations if enabled
    if GTX_1050TI_MODE and is_gtx_1050ti:
        settings["batch_size_gpu"] = 1
        settings["num_beams_gpu"] = 1  # Use greedy decoding to save memory
        settings["gpu_memory_fraction"] = 0.5  # Be very conservative with memory
        settings["gpu_memory_threshold"] = 0.4  # Switch to CPU at 40% memory usage
        settings["aggressive_memory_clearing"] = True
        # Consider using CPU for certain operations even when GPU is available
        settings["use_cpu_for_nlp"] = True  # Use CPU for NLP tasks
        settings["use_gpu_for_audio"] = True  # Keep using GPU for audio processing
    # Optimize for GPU if available (for other GPUs)
    elif settings["use_gpu"]:
        if info["gpu_memory_gb"] < 4:
            # Low VRAM GPU
            settings["batch_size_gpu"] = 1
            settings["num_beams_gpu"] = 1
            settings["gpu_memory_fraction"] = 0.6
            settings["gpu_memory_threshold"] = 0.5
        elif info["gpu_memory_gb"] < 8:
            # Mid-range GPU (6GB VRAM)
            settings["batch_size_gpu"] = 2
            settings["num_beams_gpu"] = 2
            settings["gpu_memory_fraction"] = 0.7
            settings["gpu_memory_threshold"] = 0.6
        else:
            # High-end GPU (8GB+ VRAM)
            settings["batch_size_gpu"] = 4
            settings["num_beams_gpu"] = 4
            settings["gpu_memory_fraction"] = 0.8
            settings["gpu_memory_threshold"] = 0.7

    # Set memory management settings
    settings["clear_cache_frequency"] = CLEAR_CACHE_FREQUENCY
    settings["aggressive_memory_clearing"] = AGGRESSIVE_MEMORY_CLEARING

    return settings

def setup_environment(force_cpu=False, force_gpu=False):
    """
    Set up the environment with optimized settings.

    Args:
        force_cpu (bool): If True, force CPU usage regardless of GPU availability
        force_gpu (bool): If True, force GPU usage and disable automatic switching to CPU
    """
    settings = optimize_for_system()

    # Force CPU if requested
    if force_cpu:
        settings["use_gpu"] = False
        settings["consistent_device"] = True

    # Force GPU if requested (override consistent_device setting)
    if force_gpu and torch.cuda.is_available():
        settings["use_gpu"] = True
        settings["consistent_device"] = True
        # Increase memory fraction when forcing GPU
        settings["gpu_memory_fraction"] = min(0.9, settings["gpu_memory_fraction"] + 0.1)

    # Apply GPU settings
    if settings["use_gpu"]:
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'  # Use the first GPU

        # Set more aggressive memory splitting for GTX 1050 Ti
        if "1050 Ti" in torch.cuda.get_device_name(0) and GTX_1050TI_MODE:
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
        else:
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'

        # Set GPU memory fraction
        if torch.cuda.is_available():
            torch.cuda.set_per_process_memory_fraction(settings["gpu_memory_fraction"])

            # Clear GPU memory before starting
            torch.cuda.empty_cache()

        # Enable CUDA optimizations but be careful with memory
        if settings.get("aggressive_memory_clearing", False):
            # Disable some optimizations to save memory
            torch.backends.cudnn.benchmark = False
            torch.backends.cudnn.deterministic = True
        else:
            # Enable optimizations for performance
            torch.backends.cudnn.benchmark = True
    else:
        # Disable GPU
        os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

    # Set TensorFlow memory settings
    os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'true'
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

    # Set CPU thread settings
    os.environ['OMP_NUM_THREADS'] = str(settings["max_workers"])
    os.environ['MKL_NUM_THREADS'] = str(settings["max_workers"])

    # Set PyTorch thread settings
    torch.set_num_threads(settings["max_workers"])

    # Print configuration summary
    device_type = "GPU" if settings["use_gpu"] else "CPU"
    print(f"Processing device: {device_type}")
    if settings["use_gpu"]:
        print(f"GPU memory fraction: {settings['gpu_memory_fraction']}")
        print(f"GPU memory threshold: {settings.get('gpu_memory_threshold', 'N/A')}")
        print(f"Batch size: {settings.get('batch_size_gpu', 'N/A')}")
        print(f"Beam search size: {settings.get('num_beams_gpu', 'N/A')}")

    if settings.get("consistent_device", False):
        print(f"Device consistency: Enabled (will use {device_type} consistently)")
    else:
        print("Device consistency: Disabled (may switch between GPU and CPU)")

    return settings

def clear_memory(aggressive=False):
    """
    Clear memory caches.

    Args:
        aggressive (bool): If True, perform more aggressive memory clearing
    """
    # Run garbage collection multiple times for better cleanup
    for _ in range(3 if aggressive else 1):
        gc.collect()

    if torch.cuda.is_available():
        # Empty CUDA cache
        torch.cuda.empty_cache()

        if aggressive:
            # More aggressive GPU memory management
            for i in range(torch.cuda.device_count()):
                # Get current memory usage
                used_memory = torch.cuda.memory_allocated(i)
                total_memory = torch.cuda.get_device_properties(i).total_memory
                memory_usage = used_memory / total_memory

                print(f"GPU {i} memory usage: {memory_usage:.1%}")

                # If memory usage is high, try to force release some tensors
                if memory_usage > 0.5:  # If using more than 50% of memory
                    print(f"High memory usage on GPU {i}, performing aggressive cleanup")
                    # Create and delete a dummy tensor to trigger memory cleanup
                    dummy = torch.zeros(1, device=f'cuda:{i}')
                    del dummy
                    torch.cuda.empty_cache()

    # Return current memory usage for monitoring
    memory_info = {}
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            used = torch.cuda.memory_allocated(i) / (1024**2)  # MB
            total = torch.cuda.get_device_properties(i).total_memory / (1024**2)  # MB
            memory_info[f'gpu_{i}'] = {
                'used_mb': used,
                'total_mb': total,
                'percentage': used / total * 100
            }

    # Add system RAM info
    system_ram = psutil.virtual_memory()
    memory_info['system_ram'] = {
        'used_gb': system_ram.used / (1024**3),
        'total_gb': system_ram.total / (1024**3),
        'percentage': system_ram.percent
    }

    return memory_info

def get_optimal_device(task_type=None, force_device=None):
    """
    Get the optimal device for processing based on task type and current memory usage.

    Args:
        task_type (str, optional): Type of task ('audio', 'nlp', etc.) to help determine optimal device
        force_device (str, optional): Force a specific device ('cuda' or 'cpu')

    Returns:
        torch.device: The optimal device for the task
    """
    # If device is forced, use it
    if force_device:
        if force_device == 'cuda' and torch.cuda.is_available():
            return torch.device("cuda")
        elif force_device == 'cpu':
            return torch.device("cpu")

    # If GPU is disabled or not available, use CPU
    if not ENABLE_GPU or not torch.cuda.is_available():
        return torch.device("cpu")

    # Get system settings
    settings = optimize_for_system()

    # If consistent device is enabled, stick with the initial choice
    if settings.get("consistent_device", False):
        if settings["use_gpu"]:
            return torch.device("cuda")
        else:
            return torch.device("cpu")

    # For GTX 1050 Ti with specific task types
    if GTX_1050TI_MODE and "1050 Ti" in torch.cuda.get_device_name(0):
        # Use CPU for NLP tasks if configured
        if task_type == 'nlp' and settings.get("use_cpu_for_nlp", False):
            return torch.device("cpu")
        # Use GPU for audio tasks if configured
        elif task_type == 'audio' and settings.get("use_gpu_for_audio", True):
            return torch.device("cuda")

    # Check current GPU memory usage
    if torch.cuda.is_available():
        used_memory = torch.cuda.memory_allocated(0)
        total_memory = torch.cuda.get_device_properties(0).total_memory
        memory_usage = used_memory / total_memory

        # If memory usage exceeds threshold, use CPU
        threshold = settings.get("gpu_memory_threshold", GPU_MEMORY_THRESHOLD)
        if memory_usage > threshold:
            print(f"WARNING: {task_type if task_type else 'Task'} - GPU memory usage high ({memory_usage:.1%}), switching to CPU")
            return torch.device("cpu")

    # Default to GPU if available
    return torch.device("cuda")

def get_batch_size(task_type=None):
    """
    Get the optimal batch size based on the device and task type.

    Args:
        task_type (str, optional): Type of task ('audio', 'nlp', etc.)

    Returns:
        int: Optimal batch size
    """
    device = get_optimal_device(task_type=task_type)
    settings = optimize_for_system()

    if device.type == "cuda":
        # For GTX 1050 Ti, use more conservative batch sizes
        if GTX_1050TI_MODE and "1050 Ti" in torch.cuda.get_device_name(0):
            # Check current memory usage to dynamically adjust batch size
            used_memory = torch.cuda.memory_allocated(0)
            total_memory = torch.cuda.get_device_properties(0).total_memory
            memory_usage = used_memory / total_memory

            # Adjust batch size based on current memory usage
            if memory_usage > 0.4:  # If using more than 40% of memory
                return 1  # Use smallest batch size
            else:
                return settings.get("batch_size_gpu", BATCH_SIZE_GPU)
        else:
            return settings.get("batch_size_gpu", BATCH_SIZE_GPU)
    else:
        return settings.get("batch_size_cpu", BATCH_SIZE_CPU)

def get_num_beams(task_type=None):
    """
    Get the optimal number of beams for generation based on the device and task type.

    Args:
        task_type (str, optional): Type of task ('audio', 'nlp', etc.)

    Returns:
        int: Optimal number of beams
    """
    device = get_optimal_device(task_type=task_type)
    settings = optimize_for_system()

    if device.type == "cuda":
        # For GTX 1050 Ti, use more conservative beam search
        if GTX_1050TI_MODE and "1050 Ti" in torch.cuda.get_device_name(0):
            # Check current memory usage to dynamically adjust beam search
            used_memory = torch.cuda.memory_allocated(0)
            total_memory = torch.cuda.get_device_properties(0).total_memory
            memory_usage = used_memory / total_memory

            # Adjust beam search based on current memory usage
            if memory_usage > 0.3:  # If using more than 30% of memory
                return 1  # Use greedy decoding
            else:
                return settings.get("num_beams_gpu", NUM_BEAMS_GPU)
        else:
            return settings.get("num_beams_gpu", NUM_BEAMS_GPU)
    else:
        return settings.get("num_beams_cpu", NUM_BEAMS_CPU)

def monitor_memory_usage():
    """
    Monitor current memory usage and print a report.

    Returns:
        dict: Memory usage information
    """
    memory_info = {}

    # GPU memory
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            used = torch.cuda.memory_allocated(i) / (1024**3)  # GB
            reserved = torch.cuda.memory_reserved(i) / (1024**3)  # GB
            total = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # GB

            memory_info[f'gpu_{i}'] = {
                'used_gb': used,
                'reserved_gb': reserved,
                'total_gb': total,
                'used_percent': (used / total) * 100,
                'reserved_percent': (reserved / total) * 100
            }

            print(f"GPU {i} ({torch.cuda.get_device_name(i)}):")
            print(f"  Used: {used:.2f} GB ({(used / total) * 100:.1f}%)")
            print(f"  Reserved: {reserved:.2f} GB ({(reserved / total) * 100:.1f}%)")
            print(f"  Total: {total:.2f} GB")

    # System RAM
    system_ram = psutil.virtual_memory()
    memory_info['system_ram'] = {
        'used_gb': system_ram.used / (1024**3),
        'total_gb': system_ram.total / (1024**3),
        'percent': system_ram.percent
    }

    print(f"System RAM:")
    print(f"  Used: {system_ram.used / (1024**3):.2f} GB ({system_ram.percent:.1f}%)")
    print(f"  Total: {system_ram.total / (1024**3):.2f} GB")

    return memory_info
