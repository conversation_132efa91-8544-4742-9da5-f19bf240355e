"""
Test script for GPT-2 minutes enhancement
"""
import os
import sys
import time
from datetime import timedelta

# Add the Pipeline Phase2 directory to the path
sys.path.append('Pipeline Phase2')

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    """Test GPT-2 minutes enhancement."""
    start_time = time.time()
    
    print("Testing GPT-2 minutes enhancement...")
    
    # Import the minutes enhancement function
    try:
        from gpt2_minutes_enhancer import process_minutes_for_enhancement
        print("Successfully imported GPT-2 minutes enhancement")
    except ImportError as e:
        print(f"Error importing GPT-2 minutes enhancement: {e}")
        return
    
    # Sample minutes
    sample_minutes = """
Generated Minutes of the Meeting:
Executive Summary
. <PERSON> presented the agenda of the faculty meeting focusing on key decisions, action items, and announcements.
. The evaluation will be sent lat.
. BSI program head and the sisters of the.
. The department's department activities included department announcements, department activities, IT, OJT, DIT, etc.
Key Topics Discussed:
Curriculum Updates, Department Activities, Department Announcements, Grading Policies.
Minutes of the Meeting
Department Announcements
Discussion on. Department. Announcements: <PERSON><PERSON> <PERSON> presented the agenda of the faculty meeting focusing on key decisions, action items, and announcements. Dean discussed the details of oral defense in careers, the timetable for the finals, the one day accident insurance coverage, the winner of the survey, and the. Manila day tour. Penaredondo reminds students about.
Action Items:
- Dean presented the agenda of the faculty meeting focusing on key decisions, action items, and announcements.
Curriculum Updates
The meeting was held in the faculty room. The evaluation will be sent lat. Villarica, John. Penaredondo, Maria. Miranda, Miss. Rivanova, Gener. Mosico, Harlene. Origines, Edward. Flores, the. BSI program head and the sisters of the. Ojt coordinator of.
Action Items:
- The evaluation will be sent lat.
Department Activities
Discussion on. Department. Activities: There was a faculty meeting on. IT, OJT, DIT, etc. University. Villarica will give photocopy copies of the medical results to all the students who want to inquire regarding their children's status.
Action Items:
- [Villarica] Villarica will give photocopy copies of the medical results to all the students who want to inquire regarding their children's status.
Grading Policies
There was a faculty meeting. Villarica, Gener. Mosico, Paul. Lustre, Maria. Miranda, and. Serrano were present. They discussed the upcoming physical examination on. March 11th, the cause of 2nd year inclusions, the need for. IT, and the cost of the project.
Action Items:
- March 11th, the cause of 2nd year inclusions, the need for.
Action Items Summary
- The evaluation will be sent lat.
- [Villarica] Villarica will give photocopy copies of the medical results to all the students who want to inquire regarding their children's status.
    """
    
    print("\nOriginal Minutes:")
    print("-" * 50)
    print(sample_minutes)
    print("-" * 50)
    
    # Test minutes enhancement
    print("\nEnhancing minutes...")
    enhanced_minutes = process_minutes_for_enhancement(sample_minutes)
    
    print("\nEnhanced Minutes:")
    print("-" * 50)
    print(enhanced_minutes)
    print("-" * 50)
    
    # Save the enhanced minutes to a file
    output_path = os.path.join('transcription', 'enhanced_minutes_example.txt')
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(enhanced_minutes)
    
    print(f"\nSaved enhanced minutes to: {output_path}")
    
    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nProcessing completed in {format_time(processing_time)}")

if __name__ == "__main__":
    main()
