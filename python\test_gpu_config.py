"""
Test script for GPU configuration and memory management.
This script tests different processing modes to find the optimal configuration.
"""

import os
import sys
import time
import argparse
from datetime import timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import GPU configuration
from gpu_config import (
    setup_environment, 
    get_system_info, 
    get_optimal_device, 
    get_batch_size, 
    get_num_beams,
    clear_memory,
    monitor_memory_usage,
    ENABLE_GPU,
    GTX_1050TI_MODE
)

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def test_gpu_config():
    """Test GPU configuration and print system information."""
    print("\n=== GPU Configuration Test ===\n")
    
    # Get system information
    system_info = get_system_info()
    print("System Information:")
    for key, value in system_info.items():
        print(f"  {key}: {value}")
    
    # Test different environment setups
    print("\n=== Testing Different Processing Modes ===\n")
    
    # Test GPU mode
    if system_info["gpu_available"]:
        print("\n--- GPU Mode ---")
        settings = setup_environment(force_cpu=False, force_gpu=True)
        print(f"Settings: {settings}")
        device = get_optimal_device(task_type="test")
        print(f"Optimal device: {device}")
        batch_size = get_batch_size(task_type="test")
        print(f"Batch size: {batch_size}")
        num_beams = get_num_beams(task_type="test")
        print(f"Beam search size: {num_beams}")
        
        # Test memory monitoring
        print("\nGPU Memory Usage:")
        memory_info = monitor_memory_usage()
        
        # Test memory clearing
        print("\nTesting memory clearing...")
        import torch
        if torch.cuda.is_available():
            # Allocate some tensors to use memory
            tensors = []
            for i in range(10):
                tensors.append(torch.zeros(1000, 1000, device="cuda"))
            
            print("\nMemory after allocation:")
            monitor_memory_usage()
            
            # Clear memory
            print("\nClearing memory...")
            del tensors
            memory_info = clear_memory(aggressive=True)
            
            print("\nMemory after clearing:")
            monitor_memory_usage()
    
    # Test CPU mode
    print("\n--- CPU Mode ---")
    settings = setup_environment(force_cpu=True)
    print(f"Settings: {settings}")
    device = get_optimal_device(task_type="test")
    print(f"Optimal device: {device}")
    batch_size = get_batch_size(task_type="test")
    print(f"Batch size: {batch_size}")
    num_beams = get_num_beams(task_type="test")
    print(f"Beam search size: {num_beams}")
    
    # Test GTX 1050 Ti specific optimizations
    if GTX_1050TI_MODE and system_info["gpu_available"] and "1050 Ti" in system_info.get("gpu_name", ""):
        print("\n--- GTX 1050 Ti Specific Optimizations ---")
        print("Testing task-specific device selection:")
        
        # Test different task types
        for task_type in ["audio", "nlp", "general"]:
            device = get_optimal_device(task_type=task_type)
            batch_size = get_batch_size(task_type=task_type)
            num_beams = get_num_beams(task_type=task_type)
            print(f"Task: {task_type}")
            print(f"  Device: {device}")
            print(f"  Batch size: {batch_size}")
            print(f"  Beam search size: {num_beams}")
    
    print("\n=== GPU Configuration Test Complete ===\n")

def test_processing_modes(input_file):
    """
    Test different processing modes on a sample file.
    
    Args:
        input_file (str): Path to input file to test
    """
    if not os.path.exists(input_file):
        print(f"Error: File not found: {input_file}")
        return
    
    print(f"\n=== Testing Processing Modes on {input_file} ===\n")
    
    # Import processing modules
    try:
        from fast_process import process_file
        
        # Test GPU-only mode
        if ENABLE_GPU and torch.cuda.is_available():
            print("\n--- Testing GPU-Only Mode ---\n")
            setup_environment(force_cpu=False, force_gpu=True)
            
            start_time = time.time()
            result = process_file(
                input_file, 
                "Test GPU Mode", 
                "Testing GPU-only processing", 
                "test_user"
            )
            gpu_time = time.time() - start_time
            
            print(f"GPU-only processing time: {format_time(gpu_time)} ({gpu_time:.2f} seconds)")
        
        # Test CPU-only mode
        print("\n--- Testing CPU-Only Mode ---\n")
        setup_environment(force_cpu=True)
        
        start_time = time.time()
        result = process_file(
            input_file, 
            "Test CPU Mode", 
            "Testing CPU-only processing", 
            "test_user"
        )
        cpu_time = time.time() - start_time
        
        print(f"CPU-only processing time: {format_time(cpu_time)} ({cpu_time:.2f} seconds)")
        
        # Test hybrid mode (automatic device selection)
        if ENABLE_GPU and torch.cuda.is_available():
            print("\n--- Testing Hybrid Mode (Auto Device Selection) ---\n")
            setup_environment(force_cpu=False, force_gpu=False)
            
            start_time = time.time()
            result = process_file(
                input_file, 
                "Test Hybrid Mode", 
                "Testing hybrid processing", 
                "test_user"
            )
            hybrid_time = time.time() - start_time
            
            print(f"Hybrid processing time: {format_time(hybrid_time)} ({hybrid_time:.2f} seconds)")
        
        # Compare results
        if ENABLE_GPU and torch.cuda.is_available():
            print("\n--- Processing Mode Comparison ---\n")
            print(f"GPU-only mode: {format_time(gpu_time)} ({gpu_time:.2f} seconds)")
            print(f"CPU-only mode: {format_time(cpu_time)} ({cpu_time:.2f} seconds)")
            print(f"Hybrid mode: {format_time(hybrid_time)} ({hybrid_time:.2f} seconds)")
            
            # Determine fastest mode
            fastest_time = min(gpu_time, cpu_time, hybrid_time)
            if fastest_time == gpu_time:
                fastest_mode = "GPU-only"
            elif fastest_time == cpu_time:
                fastest_mode = "CPU-only"
            else:
                fastest_mode = "Hybrid"
            
            print(f"\nFastest mode: {fastest_mode} ({format_time(fastest_time)})")
            
            # Calculate speedups
            gpu_vs_cpu = (cpu_time / gpu_time - 1) * 100 if gpu_time > 0 else 0
            hybrid_vs_cpu = (cpu_time / hybrid_time - 1) * 100 if hybrid_time > 0 else 0
            hybrid_vs_gpu = (gpu_time / hybrid_time - 1) * 100 if hybrid_time > 0 else 0
            
            print(f"GPU vs CPU: {gpu_vs_cpu:.1f}% {'faster' if gpu_vs_cpu > 0 else 'slower'}")
            print(f"Hybrid vs CPU: {hybrid_vs_cpu:.1f}% {'faster' if hybrid_vs_cpu > 0 else 'slower'}")
            print(f"Hybrid vs GPU: {hybrid_vs_gpu:.1f}% {'faster' if hybrid_vs_gpu > 0 else 'slower'}")
        else:
            print("\n--- Processing Mode Comparison ---\n")
            print(f"CPU-only mode: {format_time(cpu_time)} ({cpu_time:.2f} seconds)")
    
    except ImportError as e:
        print(f"Error importing processing modules: {e}")
        print("Make sure fast_process.py is available")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test GPU configuration and processing modes")
    parser.add_argument("--file", help="Path to input file for processing mode tests")
    parser.add_argument("--config-only", action="store_true", help="Only test GPU configuration, not processing modes")
    
    args = parser.parse_args()
    
    # Test GPU configuration
    test_gpu_config()
    
    # Test processing modes if file is provided
    if args.file and not args.config_only:
        test_processing_modes(args.file)

if __name__ == "__main__":
    main()
