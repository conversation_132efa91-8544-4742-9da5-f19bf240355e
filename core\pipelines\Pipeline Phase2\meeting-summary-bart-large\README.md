# BART Model Directory

## 📁 Purpose
This directory contains the BART (Bidirectional and Auto-Regressive Transformers) model files required for meeting minutes generation.

## 🚨 Important Notice
The large model files (model.safetensors, pytorch_model.bin) are **NOT** included in the Git repository due to GitHub's 100MB file size limit.

## 🚀 Setup Required
Before using the meeting assistant, you must download the BART model:

### Quick Setup:
```bash
python core/utils/model_downloader.py ensure
```

### Manual Setup:
See detailed instructions in: `docs/setup/MODEL_SETUP.md`

## 📋 Required Files
After setup, this directory should contain:
- ✅ `config.json` (included in repo)
- ✅ `tokenizer.json` (included in repo) 
- ✅ `vocab.json` (included in repo)
- ✅ `merges.txt` (included in repo)
- ❌ `model.safetensors` (download required - 1.5GB)
- ❌ `pytorch_model.bin` (alternative format)

## 🎓 Academic Context
Part of the Dean's Office AI-Powered Meeting Assistant
- Institution: Laguna State Polytechnic University Santa Cruz Campus
- Team: <PERSON>, <PERSON>, <PERSON><PERSON>
- Supervisor: <PERSON>, D.I.T.

---
*Run the model downloader before first use!*
