{"name": "meeting-assistant", "version": "1.0.0", "main": "src/main.js", "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "server": "node server/server.js", "server:dev": "nodemon server/server.js", "client": "set PORT=3000 && craco start", "dev": "concurrently \"npm run server\" \"npm run client\"", "dev:win": "concurrently \"npm run server\" \"set PORT=3000 && npm run start\""}, "dependencies": {"@babel/runtime": "^7.23.9", "axios": "^1.6.7", "bcrypt": "^5.1.1", "buffer": "^6.0.3", "cookie-parser": "^1.4.7", "core-js": "^3.35.1", "cors": "^2.8.5", "csurf": "^1.2.2", "docx": "^9.4.1", "docx-templates": "^4.14.1", "docxtemplater": "^3.61.1", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "form-data": "^4.0.0", "joi": "^17.12.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.0", "path-browserify": "^1.0.1", "pizzip": "^3.1.8", "process": "^0.11.10", "python-shell": "^5.0.0", "react": "^18.2.0", "react-app-rewired": "^2.2.1", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "stream-browserify": "^3.0.0", "styled-components": "^6.1.8", "util": "^0.12.5", "vm-browserify": "^1.1.2"}, "devDependencies": {"@babel/core": "^7.23.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.23.9", "@babel/preset-env": "^7.23.9", "@babel/preset-react": "^7.23.9", "@craco/craco": "^7.1.0", "autoprefixer": "^10.4.17", "babel-loader": "^9.1.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "css-loader": "^6.10.0", "electron": "^28.2.1", "electron-is-dev": "^3.0.1", "nodemon": "^3.1.9", "postcss": "^8.4.35", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^8.1.0", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^9.3.0", "style-loader": "^3.3.4", "tailwindcss": "^3.4.1", "wait-on": "^7.2.0"}, "resolutions": {"nth-check": "^2.0.1", "postcss": "^8.4.35", "serialize-javascript": "^6.0.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "overrides": {"nth-check": "^2.0.1", "postcss": "^8.4.35", "serialize-javascript": "^6.0.2"}}