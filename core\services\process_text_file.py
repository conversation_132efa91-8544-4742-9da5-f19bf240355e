#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Text File Processing Script for Meeting Assistant

This script processes text files (transcripts) directly, bypassing audio processing
and only performing translation if needed before proceeding to Phase 2 for minutes generation.
"""

import os
import sys
import json
import time
import argparse
import traceback
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import progress reporting utility
try:
    from python.progress_utils import send_progress
except ImportError:
    # Define a dummy function if the module is not available
    def send_progress(stage, progress, details, **kwargs):
        progress_data = {
            "type": "progress",
            "stage": stage,
            "progress": progress,
            "details": details
        }
        if kwargs:
            progress_data.update(kwargs)
        print(json.dumps(progress_data), file=sys.stderr)

# Import the translator from Phase 1
try:
    print("Trying to import GoogleTranslator from python.Pipeline_Phase1.PHASE1...")
    from python.Pipeline_Phase1.PHASE1 import GoogleTranslator
    print("Successfully imported GoogleTranslator from python.Pipeline_Phase1.PHASE1")
except ImportError as e:
    print(f"First import attempt failed: {e}")
    try:
        print("Trying to import GoogleTranslator from Pipeline_Phase1.PHASE1...")
        from Pipeline_Phase1.PHASE1 import GoogleTranslator
        print("Successfully imported GoogleTranslator from Pipeline_Phase1.PHASE1")
    except ImportError as e:
        print(f"Second import attempt failed: {e}")
        try:
            print("Trying to import GoogleTranslator directly...")
            from deep_translator import GoogleTranslator
            print("Successfully imported GoogleTranslator from deep_translator")
        except ImportError as e:
            print(f"Failed to import GoogleTranslator: {e}")
            print("Installing deep_translator...")
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "deep_translator"])
            from deep_translator import GoogleTranslator
            print("Successfully installed and imported GoogleTranslator")

# Try to import Phase 2 for minutes generation
try:
    print("Trying to import Phase2V2 from python.Pipeline_Phase2.Phase2V2...")
    from python.Pipeline_Phase2.Phase2V2 import process_meeting_transcript_enhanced as phase2v2_process
    print("Successfully imported Phase2V2 from python.Pipeline_Phase2.Phase2V2")
except ImportError as e:
    print(f"First Phase2 import attempt failed: {e}")
    try:
        print("Trying to import Phase2V2 from Pipeline_Phase2.Phase2V2...")
        from Pipeline_Phase2.Phase2V2 import process_meeting_transcript_enhanced as phase2v2_process
        print("Successfully imported Phase2V2 from Pipeline_Phase2.Phase2V2")
    except ImportError as e:
        print(f"Second Phase2 import attempt failed: {e}")
        try:
            print("Trying to import original PHASE2 from python.Pipeline_Phase2.PHASE2...")
            from python.Pipeline_Phase2.PHASE2 import process_meeting_transcript_enhanced
            phase2v2_process = process_meeting_transcript_enhanced
            print("Successfully imported original PHASE2 from python.Pipeline_Phase2.PHASE2")
        except ImportError as e:
            print(f"Third Phase2 import attempt failed: {e}")
            try:
                print("Trying to import original PHASE2 from Pipeline_Phase2.PHASE2...")
                from Pipeline_Phase2.PHASE2 import process_meeting_transcript_enhanced
                phase2v2_process = process_meeting_transcript_enhanced
                print("Successfully imported original PHASE2 from Pipeline_Phase2.PHASE2")
            except ImportError as e:
                print(f"All Phase2 import attempts failed: {e}")
                print("Error: Could not import Phase2 modules for minutes generation")
                sys.exit(1)

def read_text_file(file_path):
    """Read text from a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # Try with different encodings if UTF-8 fails
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading file with latin-1 encoding: {e}")
            raise

def translate_text(text, source_lang='tl', target_lang='en'):
    """Translate text from source language to target language."""
    translator = GoogleTranslator(source=source_lang, target=target_lang)

    # For very long texts, split into chunks to avoid API limits
    if len(text) > 5000:
        chunks = []
        for i in range(0, len(text), 5000):
            chunks.append(text[i:i+5000])

        translated_chunks = []
        for i, chunk in enumerate(chunks):
            send_progress("Translating", 30 + (i / len(chunks) * 30),
                         f"Translating chunk {i+1}/{len(chunks)}")
            translated_chunks.append(translator.translate(chunk))

        return ''.join(translated_chunks)
    else:
        return translator.translate(text)

def process_text_file(input_file, output_dir, language='tagalog', title=None):
    """
    Process a text file (transcript) and generate minutes.

    Args:
        input_file (str): Path to the input text file
        output_dir (str): Directory to save output files
        language (str): Language of the input text ('tagalog' or 'english')
        title (str): Title for the meeting minutes

    Returns:
        dict: Processing results
    """
    start_time = time.time()

    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    transcription_dir = os.path.join(output_dir, "transcription")
    minutes_dir = os.path.join(output_dir, "minutes of the meeting")
    os.makedirs(transcription_dir, exist_ok=True)
    os.makedirs(minutes_dir, exist_ok=True)

    # Generate filenames
    timestamp = int(time.time() * 1000)
    base_filename = os.path.splitext(os.path.basename(input_file))[0]
    safe_filename = "".join([c if c.isalnum() or c in " ._-" else "_" for c in base_filename])

    if title:
        safe_title = "".join([c if c.isalnum() or c in " ._-" else "_" for c in title])
        output_base = f"{timestamp}-{safe_title}"
    else:
        output_base = f"{timestamp}-{safe_filename}"

    # Read the input file
    send_progress("Processing", 10, "Reading text file")
    text_content = read_text_file(input_file)

    # Process based on language
    if language.lower() == 'tagalog':
        # Save original Tagalog transcript
        tagalog_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript_tagalog.txt")
        with open(tagalog_transcript_path, 'w', encoding='utf-8') as f:
            f.write(text_content)

        send_progress("Translating", 20, "Translating Tagalog text to English")

        # Translate to English
        try:
            english_text = translate_text(text_content, source_lang='tl', target_lang='en')

            # Save translated English transcript
            english_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript.txt")
            with open(english_transcript_path, 'w', encoding='utf-8') as f:
                f.write(english_text)

            # Use the English text for minutes generation
            transcript_for_minutes = english_text
            transcript_path = english_transcript_path

        except Exception as e:
            print(f"Translation error: {e}")
            print("Using original Tagalog text for minutes generation")
            transcript_for_minutes = text_content
            transcript_path = tagalog_transcript_path
    else:
        # Already in English, save directly
        send_progress("Processing", 20, "Processing English text")
        english_transcript_path = os.path.join(transcription_dir, f"{output_base}_transcript.txt")
        with open(english_transcript_path, 'w', encoding='utf-8') as f:
            f.write(text_content)

        transcript_for_minutes = text_content
        transcript_path = english_transcript_path

    # Generate minutes
    send_progress("Generating Minutes", 60, "Analyzing transcript content and generating minutes")

    try:
        # Process the transcript to generate minutes
        minutes_content = phase2v2_process(transcript_for_minutes, include_executive_summary=True)

        # Save minutes to file
        minutes_path = os.path.join(minutes_dir, f"{output_base}_minutes.md")
        with open(minutes_path, 'w', encoding='utf-8') as f:
            f.write(minutes_content)

        send_progress("Generating Minutes", 100, "Minutes generation complete")

    except Exception as e:
        print(f"Error generating minutes: {e}")
        traceback.print_exc()

        # Create a simple error message as minutes
        error_message = f"# Minutes Generation Failed\n\nThe system was unable to generate minutes due to the following error:\n\n```\n{str(e)}\n```\n\n## Transcript\n\nPlease refer to the transcript file for the meeting content."
        minutes_path = os.path.join(minutes_dir, f"{output_base}_minutes.md")
        with open(minutes_path, 'w', encoding='utf-8') as f:
            f.write(error_message)

        minutes_content = error_message

    # Calculate processing time
    processing_time = time.time() - start_time

    # Prepare result
    result = {
        "status": "success",
        "processing_time": processing_time,
        "processing_time_formatted": f"{processing_time:.2f} seconds",
        "result": {
            "data": {
                "transcript_file": transcript_path,
                "minutes_file": minutes_path,
                "transcript": transcript_for_minutes,
                "minutes": minutes_content,
                "speakers": [],  # No speaker detection for text files
                "transcription_dir": transcription_dir,
                "minutes_dir": minutes_dir,
                "job_id": f"text_{timestamp}"
            }
        }
    }

    return result

def main():
    print("Starting text file processing script...")

    try:
        parser = argparse.ArgumentParser(description="Process text files for meeting minutes")
        parser.add_argument("input_file", help="Path to the input text file")
        parser.add_argument("--output_dir", help="Directory to save output files", default=None)
        parser.add_argument("--language", help="Language of the input text (tagalog or english)", default="tagalog")
        parser.add_argument("--title", help="Title for the meeting minutes", default=None)

        args = parser.parse_args()

        print(f"Arguments parsed: input_file={args.input_file}, language={args.language}, title={args.title}")

        # Check if input file exists
        if not os.path.exists(args.input_file):
            print(f"Error: Input file not found: {args.input_file}")
            print(f"Current working directory: {os.getcwd()}")
            print(f"Directory contents: {os.listdir(os.path.dirname(args.input_file) or '.')}")
            raise FileNotFoundError(f"Input file not found: {args.input_file}")

        # Set default output directory if not provided
        if not args.output_dir:
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            args.output_dir = os.path.join(base_dir, "uploads", "processed", f"text_{int(time.time())}")
            print(f"Using default output directory: {args.output_dir}")

        try:
            print(f"Processing text file: {args.input_file}")
            result = process_text_file(args.input_file, args.output_dir, args.language, args.title)

            # Print result as JSON
            print("===JSON_OUTPUT_START===")
            print(json.dumps(result, indent=2))
            print("===JSON_OUTPUT_END===")

        except Exception as e:
            print(f"Error processing text file: {e}")
            print(traceback.format_exc())
            print("===JSON_OUTPUT_START===")
            print(json.dumps({
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }, indent=2))
            print("===JSON_OUTPUT_END===")
            sys.exit(1)

    except Exception as e:
        print(f"Error in main function: {e}")
        print(traceback.format_exc())
        print("===JSON_OUTPUT_START===")
        print(json.dumps({
            "status": "error",
            "error": f"Error in main function: {str(e)}",
            "traceback": traceback.format_exc()
        }, indent=2))
        print("===JSON_OUTPUT_END===")
        sys.exit(1)

if __name__ == "__main__":
    main()
