import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import Sidebar from './Sidebar';
import backIcon from '../assets/back.png';
import dateIcon from '../assets/date.png';
import timeIcon from '../assets/time.png';
import speaker from '../assets/speaker.png';
import { sharedStyles } from '../styles/SharedStyles';
import { saveTranscript, getTranscriptById } from '../utils/transcriptStorage';
import { removeNamesInBrackets } from '../utils/sanitizeContent';



const Conversation = () => {
  const { id } = useParams(); // Get the transcription ID from URL params
  // We use window.history.back() instead of navigate
  const [autoScroll, setAutoScroll] = useState(false);
  const [transcriptData, setTranscriptData] = useState([]);
  const [meetingTitle, setMeetingTitle] = useState('CCS Faculty Meeting');
  const [meetingDate, setMeetingDate] = useState('4/29/2025');
  const [meetingTime, setMeetingTime] = useState('9:08:27 AM');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTagalog, setIsTagalog] = useState(true);
  const transcriptRef = useRef(null);  // Add ref for transcript section

  // Auto scroll effect with manual scroll detection
  useEffect(() => {
    let scrollInterval = null;
    let userScrollTimeout = null;
    let isUserScrolling = false;

    // Function to handle user scrolling
    const handleUserScroll = () => {
      if (!isUserScrolling) {
        isUserScrolling = true;
      }

      // Clear any existing timeout
      if (userScrollTimeout) {
        clearTimeout(userScrollTimeout);
      }

      // Set a timeout to resume auto-scrolling after user stops scrolling
      userScrollTimeout = setTimeout(() => {
        isUserScrolling = false;
      }, 4000); // Wait 4 seconds after user stops scrolling
    };

    // Add event listeners for user scrolling
    if (transcriptRef.current) {
      // Mouse wheel scrolling
      transcriptRef.current.addEventListener('wheel', handleUserScroll, { passive: true });

      // Touch scrolling
      transcriptRef.current.addEventListener('touchmove', handleUserScroll, { passive: true });

      // Scrollbar dragging
      transcriptRef.current.addEventListener('scroll', handleUserScroll, { passive: true });
    }

    // Set up auto-scrolling if enabled
    if (autoScroll) {
      scrollInterval = setInterval(() => {
        const container = transcriptRef.current;
        if (container && !isUserScrolling) {
          // Calculate how far we are from the bottom
          const scrollBottom = container.scrollHeight - container.scrollTop - container.clientHeight;

          // If we're at the bottom, reset to top
          if (scrollBottom < 5) {
            container.scrollTop = 0;
          } else {
            // Otherwise continue scrolling
            container.scrollTop += 1; // Very slow scroll speed for better readability
          }
        }
      }, 50);
    }

    // Cleanup function
    return () => {
      if (scrollInterval) {
        clearInterval(scrollInterval);
      }

      if (userScrollTimeout) {
        clearTimeout(userScrollTimeout);
      }

      if (transcriptRef.current) {
        transcriptRef.current.removeEventListener('wheel', handleUserScroll);
        transcriptRef.current.removeEventListener('touchmove', handleUserScroll);
        transcriptRef.current.removeEventListener('scroll', handleUserScroll);
      }
    };
  }, [autoScroll]);

  // Apply no-scrollbar class to document when component mounts
  useEffect(() => {
    // Add classes to hide scrollbars
    document.documentElement.classList.add('no-scrollbar');
    document.body.classList.add('no-scrollbar');

    // Force scrollbars to be hidden with inline styles as well
    document.documentElement.style.overflow = 'hidden';
    document.documentElement.style.msOverflowStyle = 'none';
    document.documentElement.style.scrollbarWidth = 'none';

    document.body.style.overflow = 'hidden';
    document.body.style.msOverflowStyle = 'none';
    document.body.style.scrollbarWidth = 'none';

    // Cleanup when component unmounts
    return () => {
      document.documentElement.classList.remove('no-scrollbar');
      document.body.classList.remove('no-scrollbar');

      // Reset inline styles
      document.documentElement.style.overflow = '';
      document.documentElement.style.msOverflowStyle = '';
      document.documentElement.style.scrollbarWidth = '';

      document.body.style.overflow = '';
      document.body.style.msOverflowStyle = '';
      document.body.style.scrollbarWidth = '';
    };
  }, []);

  // Helper function to parse transcript text
  const parseTranscript = (text) => {
    if (!text || typeof text !== 'string') {
      console.error('Invalid transcript text:', text);
      return [];
    }

    try {
      // First, remove names in square brackets from the entire text
      text = removeNamesInBrackets(text);

      return text.split('\n')
        .filter(line => line.trim() !== '') // Remove empty lines
        .map(line => {
          // Check if the line contains a colon
          if (line.includes(':')) {
            const [speaker, ...textParts] = line.split(':');
            return {
              speaker: speaker.trim(),
              text: textParts.join(':').trim() // Rejoin in case text contains colons
            };
          }
          // If no colon, try to handle it as a complete line
          if (line.trim()) {
            return {
              speaker: "Unknown",
              text: line.trim()
            };
          }
          return null;
        })
        .filter(item => item !== null); // Remove any null entries
    } catch (error) {
      console.error('Error parsing transcript:', error);
      return [];
    }
  };

  // We now use localStorage as the single source of truth for transcript data

  // Function to update transcript with new timestamps
  const updateTranscriptTimestamps = (transcriptData) => {
    // Update the transcript in localStorage with new timestamps
    const updatedTranscript = saveTranscript(transcriptData, false);

    // Update the UI with the new timestamp
    if (updatedTranscript.updatedAt) {
      try {
        const date = new Date(updatedTranscript.updatedAt);
        setMeetingDate(date.toLocaleDateString());
        setMeetingTime(date.toLocaleTimeString());
      } catch (e) {
        console.error('Error parsing updated timestamp:', e);
      }
    }

    return updatedTranscript;
  };

  // Load transcript data based on ID
  useEffect(() => {
    setLoading(true);
    setError(null);

    // Create Tagalog transcript data as fallback
    const fallbackTranscript = [
      { speaker: "Dean Mia V. Villarica", text: "Magandang umaga sa lahat, pasensya na po sa pagkaantala. Ito po ang ating April Faculty Meeting at ginawa natin ito online dahil karamihan sa inyo ay online din para ma-accommodate natin ngayon." },
      { speaker: "Unknown", text: "Ito po ang ating mga agenda na tatalakayin ngayon" },
      { speaker: "Dean Mia V. Villarica", text: "Ang ating Education Tour at iba pang mga aktibidad, accreditation preparations para sa bees, ang IPCR para sa permanent faculty members, ang ating OJT related reports at ang PEN reports mula sa program heads, at mga paalala tungkol sa ating Seaman Jazz." }
    ];

    // Remove any names in square brackets from the fallback transcript
    fallbackTranscript.forEach(entry => {
      entry.speaker = removeNamesInBrackets(entry.speaker);
      entry.text = removeNamesInBrackets(entry.text);
    });

    console.log('Loading transcript data for ID:', id);

    // SINGLE SOURCE OF TRUTH: Use currentTranscript from localStorage as the primary source
    // This is where the most recent transcript data is stored after processing
    const storedTranscript = localStorage.getItem('currentTranscript');

    // Special case for 'current' ID - always use the currentTranscript
    if (id === 'current' && storedTranscript) {
      try {
        const transcriptData = JSON.parse(storedTranscript);
        console.log('Found current transcript in localStorage:', transcriptData);

        // Declare contentToUse variable at this scope level
        let contentToUse;

        // Check if the transcript has an explicit language flag (either is_tagalog or type property)
        if (transcriptData.hasOwnProperty('is_tagalog') || transcriptData.hasOwnProperty('type')) {
          // Determine language from either is_tagalog flag or type property
          const isTagalogContent = transcriptData.hasOwnProperty('is_tagalog')
            ? transcriptData.is_tagalog
            : (transcriptData.type === 'Tagalog');

          console.log('Using explicit language flag:', isTagalogContent, 'Source:',
            transcriptData.hasOwnProperty('is_tagalog') ? 'is_tagalog property' : 'type property');

          setIsTagalog(isTagalogContent);

          // Use the appropriate content based on the language flag
          contentToUse = isTagalogContent ?
            transcriptData.original_transcript :
            transcriptData.transcript;

          // If the selected content is empty, try the other language as fallback
          if (!contentToUse || contentToUse.trim() === '') {
            console.log('Selected language content is empty, trying fallback');
            contentToUse = isTagalogContent ?
              transcriptData.transcript :
              transcriptData.original_transcript;
          }
        } else {
          // Legacy behavior - prioritize Tagalog (original_transcript)
          contentToUse = transcriptData.original_transcript;
          let isTagalogContent = true;

          // If no Tagalog content is available, fall back to English
          if (!contentToUse || contentToUse.trim() === '') {
            console.log('No Tagalog content available, falling back to English transcript');
            contentToUse = transcriptData.transcript;
            isTagalogContent = false;
          }

          // Update the isTagalog state
          setIsTagalog(isTagalogContent);
        }

        // Parse the transcript content
        if (contentToUse) {
          const parsedData = parseTranscript(contentToUse);
          console.log('Parsed transcript data:', parsedData);

          if (parsedData && parsedData.length > 0) {
            setTranscriptData(parsedData);

            // Update meeting metadata
            if (transcriptData.title) {
              setMeetingTitle(transcriptData.title);
            }

            // Update timestamps in UI
            if (transcriptData.timestamp) {
              try {
                const date = new Date(transcriptData.timestamp);
                setMeetingDate(date.toLocaleDateString());
                setMeetingTime(date.toLocaleTimeString());
              } catch (e) {
                console.error('Error parsing timestamp:', e);
              }
            }

            setLoading(false);
            return; // Exit early since we found a transcript
          }
        }

        // If we get here, we couldn't parse the transcript content
        console.error('Failed to parse transcript content');
        setError('Failed to parse transcript content');
        setLoading(false);

        // This section is now handled by the code above
      } catch (e) {
        console.error('Error parsing stored transcript:', e);
      }
    }

    // For numeric IDs, check savedTranscripts
    if (id && !isNaN(parseInt(id))) {
      const savedTranscript = getTranscriptById(parseInt(id));
      if (savedTranscript) {
        console.log('Found transcript in savedTranscripts:', savedTranscript);

        // Prioritize Tagalog transcript if available
        if (savedTranscript.original_transcript) {
          try {
            console.log('Using Tagalog transcript from savedTranscripts');

            // Check if original_transcript is actually in Tagalog by looking for common Tagalog words
            const tagalogWords = ['ang', 'mga', 'sa', 'ng', 'po', 'natin', 'para', 'ito'];
            const hasTagalogWords = tagalogWords.some(word =>
              savedTranscript.original_transcript &&
              savedTranscript.original_transcript.toLowerCase().includes(` ${word} `)
            );

            console.log('Contains Tagalog words:', hasTagalogWords);

            // If it doesn't contain Tagalog words, it might be English content in the original_transcript field
            if (!hasTagalogWords && savedTranscript.original_transcript_file) {
              console.log('Will attempt to fetch Tagalog content from file');

              // Try to fetch the Tagalog transcript file
              fetch(`http://localhost:3001/api/file?type=tagalog&fileId=${savedTranscript.id || savedTranscript.job_id}`)
                .then(response => {
                  if (!response.ok) {
                    // If that fails, try with the file path
                    console.log('Failed to fetch by ID, trying with file path');
                    return fetch(`http://localhost:3001/api/file?path=${encodeURIComponent(savedTranscript.original_transcript_file)}&type=tagalog`);
                  }
                  return response;
                })
                .then(response => {
                  if (!response.ok) {
                    throw new Error(`Failed to fetch Tagalog transcript: ${response.status}`);
                  }
                  return response.text();
                })
                .then(tagalogText => {
                  if (tagalogText && tagalogText.trim() !== '') {
                    console.log('Successfully fetched Tagalog text from file');

                    // Update the transcript data with the fetched Tagalog text
                    savedTranscript.original_transcript = tagalogText;

                    // Save the updated transcript data to localStorage
                    localStorage.setItem('currentTranscript', JSON.stringify(savedTranscript));

                    // Parse and display the Tagalog transcript
                    const parsedTagalogData = parseTranscript(tagalogText);
                    if (parsedTagalogData && parsedTagalogData.length > 0) {
                      setTranscriptData(parsedTagalogData);
                      setLoading(false);
                    }
                  }
                })
                .catch(error => {
                  console.error('Error fetching Tagalog transcript from file:', error);
                });
            }

            const parsedData = parseTranscript(savedTranscript.original_transcript);
            console.log('Parsed Tagalog transcript data:', parsedData);

            if (parsedData && parsedData.length > 0) {
              setTranscriptData(parsedData);

              // Update meeting metadata
              if (savedTranscript.title) {
                setMeetingTitle(savedTranscript.title);
              }

              // Update timestamps in UI
              if (savedTranscript.updatedAt) {
                try {
                  const date = new Date(savedTranscript.updatedAt);
                  setMeetingDate(date.toLocaleDateString());
                  setMeetingTime(date.toLocaleTimeString());
                } catch (e) {
                  console.error('Error parsing timestamp:', e);
                }
              }

              // Update currentTranscript for consistency
              localStorage.setItem('currentTranscript', JSON.stringify(savedTranscript));

              setLoading(false);
              return; // Exit early since we found a Tagalog transcript
            }
          } catch (e) {
            console.error('Error parsing Tagalog transcript from savedTranscripts:', e);
          }
        }

        // Fall back to English transcript if Tagalog is not available
        if (savedTranscript.transcript) {
          try {
            console.log('Falling back to English transcript from savedTranscripts');
            const parsedData = parseTranscript(savedTranscript.transcript);
            console.log('Parsed English transcript data:', parsedData);

            if (parsedData && parsedData.length > 0) {
              setTranscriptData(parsedData);

              // Update meeting metadata
              if (savedTranscript.title) {
                setMeetingTitle(savedTranscript.title);
              }

              // Update timestamps in UI
              if (savedTranscript.updatedAt) {
                try {
                  const date = new Date(savedTranscript.updatedAt);
                  setMeetingDate(date.toLocaleDateString());
                  setMeetingTime(date.toLocaleTimeString());
                } catch (e) {
                  console.error('Error parsing timestamp:', e);
                }
              }

              // Update currentTranscript for consistency
              localStorage.setItem('currentTranscript', JSON.stringify(savedTranscript));

              setLoading(false);
              return; // Exit early since we found a transcript
            }
          } catch (e) {
            console.error('Error parsing English transcript from savedTranscripts:', e);
          }
        }
      }
    }

    // If we reach here, we couldn't find a valid transcript
    console.warn('No valid transcript found, using fallback data');
    setTranscriptData(fallbackTranscript);
    setError('Could not load transcript. Using fallback data.');
    setLoading(false);
  }, [id]);

  const toggleAutoScroll = () => {
    setAutoScroll(!autoScroll);
  };





  // Function to handle editing the transcript
  const handleEditTranscript = (index, newText) => {
    // Remove any names in square brackets from the edited text
    newText = removeNamesInBrackets(newText);

    // Create a copy of the transcript data
    const updatedTranscriptData = [...transcriptData];

    // Update the text at the specified index
    updatedTranscriptData[index].text = newText;

    // Update the state
    setTranscriptData(updatedTranscriptData);

    // Get the current transcript from localStorage
    const storedTranscript = localStorage.getItem('currentTranscript');

    if (storedTranscript) {
      try {
        const transcriptObj = JSON.parse(storedTranscript);

        // Reconstruct the transcript text from the updated data
        const updatedTranscriptText = updatedTranscriptData
          .map(entry => `${entry.speaker}: ${entry.text}`)
          .join('\n');

        // Check if we're using the Tagalog transcript
        if (transcriptObj.original_transcript) {
          console.log('Updating Tagalog transcript');
          // Update the Tagalog transcript text in the object
          transcriptObj.original_transcript = updatedTranscriptText;
        } else {
          console.log('Updating English transcript (no Tagalog transcript found)');
          // Fall back to updating the English transcript if no Tagalog transcript exists
          transcriptObj.transcript = updatedTranscriptText;
        }

        // Save with updated timestamp
        updateTranscriptTimestamps(transcriptObj);

        console.log('Transcript updated with new timestamps');
      } catch (e) {
        console.error('Error updating transcript:', e);
      }
    }
  };



  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      fontFamily: 'Montserrat, sans-serif',
      position: 'relative',
      overflow: 'hidden',
      msOverflowStyle: 'none',
      scrollbarWidth: 'none'
    }} className="conversation-container">
      {/* Background Pattern */}
      <div className="background-pattern" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          linear-gradient(45deg, rgba(3, 79, 175, 0.1), rgba(87, 215, 226, 0.1)),
          url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23034FAF' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
        `,
        animation: 'backgroundShift 30s linear infinite',
        zIndex: 0,
      }} />

      {/* Gradient Overlay */}
      <div className="gradient-overlay" style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(3, 79, 175, 0.25) 100%)',
        zIndex: 1,
      }} />

      {/* Decorative Elements */}
      <div className="decorative-circle circle-1" style={{
        position: 'absolute',
        width: '400px',
        height: '400px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(3, 79, 175, 0.25) 0%, rgba(87, 215, 226, 0.12) 70%)',
        top: '-150px',
        right: '5%',
        animation: 'float 15s ease-in-out infinite, shimmer 8s infinite',
        zIndex: 1,
        opacity: 0.7,
        filter: 'blur(80px)',
      }} />

      <div className="decorative-circle circle-2" style={{
        position: 'absolute',
        width: '300px',
        height: '300px',
        borderRadius: '50%',
        background: 'radial-gradient(circle, rgba(87, 215, 226, 0.25) 0%, rgba(3, 79, 175, 0.12) 70%)',
        bottom: '5%',
        left: '25%',
        animation: 'float 12s ease-in-out infinite reverse, shimmer 10s infinite 2s',
        zIndex: 1,
        opacity: 0.7,
        filter: 'blur(80px)',
      }} />

      <Sidebar style={{ position: 'relative', zIndex: 2 }} />

      <div style={{
        flex: 1,
        padding: '30px',
        height: '100vh',
        overflow: 'hidden',
        position: 'relative',
        zIndex: 2,
        display: 'flex',
        flexDirection: 'column',
        msOverflowStyle: 'none',
        scrollbarWidth: 'none',
      }} className="content-container">
        {/* Header */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '25px',
          flexShrink: 0,
          background: 'rgba(255, 255, 255, 0.15)',
          padding: '15px 25px',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.25)',
          transition: 'all 0.3s ease',
          animation: 'fadeIn 0.8s ease-out',
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            padding: '12px',
            borderRadius: '50%',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.05)';
            e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
            e.currentTarget.style.background = 'transparent';
          }}
          >
            <img
              src={backIcon}
              alt="back"
              style={{
                width: '24px',
                height: '24px',
                marginRight: '15px',
                cursor: 'pointer',
                transition: 'transform 0.3s ease'
              }}
              onClick={() => window.history.back()}
            />
          </div>
          <h1 style={{
            margin: 0,
            fontSize: "24px",
            fontWeight: "900",
            color: '#034FAF',
            letterSpacing: '0.5px',
            cursor: 'default',
            textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
          }}>{isTagalog ? 'TAGALOG TRANSCRIPT' : 'ENGLISH TRANSCRIPT'}</h1>
        </div>

        {/* Title Header */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.15)',
          padding: '25px',
          borderRadius: '15px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          marginBottom: '25px',
          border: '1px solid rgba(255, 255, 255, 0.25)',
          flexShrink: 0,
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          animation: 'fadeIn 0.8s ease-out',
        }}>
          <h2 style={{
            margin: 0,
            fontSize: '24px',
            fontWeight: '800',
            color: '#034FAF',
            marginBottom: '12px',
            cursor: 'default',
            textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
          }}>
            {meetingTitle}
          </h2>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                background: 'rgba(255, 255, 255, 0.3)',
                padding: '8px 12px',
                borderRadius: '8px',
                border: '1px solid rgba(255, 255, 255, 0.3)',
              }}>
                <img src={dateIcon} alt="Date" style={{ width: '20px', height: '20px', marginRight: '8px', cursor: 'default' }} />
                <span style={{
                  fontSize: '15px',
                  fontWeight: '600',
                  color: '#333',
                  cursor: 'default',
                }}>{meetingDate}</span>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                background: 'rgba(255, 255, 255, 0.3)',
                padding: '8px 12px',
                borderRadius: '8px',
                border: '1px solid rgba(255, 255, 255, 0.3)',
              }}>
                <img src={timeIcon} alt="Time" style={{ width: '20px', height: '20px', marginRight: '8px', cursor: 'default' }} />
                <span style={{
                  fontSize: '15px',
                  fontWeight: '600',
                  color: '#333',
                  cursor: 'default',
                }}>{meetingTime}</span>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '15px',
            }}>


              {/* Auto-scroll Toggle */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                background: 'rgba(255, 255, 255, 0.3)',
                padding: '8px 15px',
                borderRadius: '10px',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
              }}>
                <span style={{
                  fontSize: '14px',
                  fontWeight: '600',
                  marginRight: '12px',
                  color: '#034FAF',
                  cursor: 'default',
                }}>Auto-scroll</span>
                <div
                  onClick={toggleAutoScroll}
                  style={{
                    width: '44px',
                    height: '24px',
                    borderRadius: '12px',
                    backgroundColor: autoScroll ? '#034FAF' : '#E0E0E0',
                    position: 'relative',
                    cursor: 'pointer',
                    transition: 'background-color 0.3s ease',
                    boxShadow: '0 2px 5px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <div
                    style={{
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      backgroundColor: 'white',
                      position: 'absolute',
                      top: '2px',
                      left: autoScroll ? '22px' : '2px',
                      transition: 'left 0.3s ease',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Transcript Section */}
        <div
          ref={transcriptRef}
          style={{
            background: 'rgba(255, 255, 255, 0.15)',
            padding: '30px',
            borderRadius: '15px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
            overflowY: 'scroll', // Changed from 'auto' to 'scroll' to force scrollbar
            flex: 1,
            border: '1px solid rgba(255, 255, 255, 0.25)',
            marginBottom: '0',
            scrollBehavior: autoScroll ? 'smooth' : 'auto',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            animation: 'fadeIn 0.8s ease-out',
            overflowX: 'hidden',
          }}
          className="transcript-section manual-scroll"
          onWheel={(e) => {
            // Prevent auto-scroll from interfering with manual scrolling
            if (autoScroll) {
              e.stopPropagation();
            }
          }}
        >
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              flexDirection: 'column',
              gap: '20px'
            }}>
              <div className="loading-spinner" style={{
                width: '50px',
                height: '50px',
                border: '5px solid rgba(3, 79, 175, 0.1)',
                borderRadius: '50%',
                borderTop: '5px solid #034FAF',
                animation: 'spin 1s linear infinite'
              }} />
              <p style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#034FAF'
              }}>Loading transcript...</p>
            </div>
          ) : error ? (
            <div style={{
              padding: '20px',
              background: 'rgba(255, 0, 0, 0.1)',
              borderRadius: '8px',
              color: '#d32f2f',
              margin: '20px 0'
            }}>
              <h3>Error Loading Transcript</h3>
              <p>{error}</p>

              {/* Display fallback transcript data even when there's an error */}
              {transcriptData && transcriptData.length > 0 && (
                <div style={{ marginTop: '20px' }}>
                  <h4>Using fallback transcript data:</h4>
                </div>
              )}
            </div>
          ) : !transcriptData || transcriptData.length === 0 ? (
            <div style={{
              padding: '20px',
              background: 'rgba(255, 255, 0, 0.1)',
              borderRadius: '8px',
              color: '#856404',
              margin: '20px 0'
            }}>
              <h3>No Transcript Data Available</h3>
              <p>The transcript appears to be empty. Please try uploading the file again or select a different transcript.</p>
            </div>
          ) : (
            /* Display transcript data */
            transcriptData.map((entry, index) => (
              <div
                key={index}
                style={{
                  marginBottom: '25px',
                  padding: '20px',
                  borderRadius: '12px',
                  background: index % 2 === 0 ? 'rgba(255, 255, 255, 0.5)' : 'rgba(255, 255, 255, 0.3)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  backdropFilter: 'blur(5px)',
                  WebkitBackdropFilter: 'blur(5px)',
                  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.03)',
                  animation: `fadeIn 0.5s ease-out ${index * 0.1}s both`,
                  transition: 'all 0.3s ease',
                  width: '100%',
                  boxSizing: 'border-box',
                }}
                className="transcript-entry"
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.03)';
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
                  <div style={{
                    borderRadius: "50%",
                    padding: "10px",
                    background: 'rgba(3, 79, 175, 0.1)',
                    border: '1px solid rgba(3, 79, 175, 0.2)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <img
                      src={speaker}
                      alt="Speaker Icon"
                      style={{ width: "24px", height: "24px", cursor: 'default' }}
                    />
                  </div>
                  <div style={{ flex: 1 }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '8px',
                    }}>
                      <span style={{
                        fontSize: "16px",
                        fontWeight: "700",
                        color: "#034FAF",
                        cursor: 'default',
                        textShadow: '0 1px 2px rgba(255, 255, 255, 0.7)',
                      }}>
                        {removeNamesInBrackets(entry.speaker)}
                      </span>
                    </div>
                    <div style={{
                      position: 'relative',
                    }}>
                      <p
                        style={{
                          fontSize: "15px",
                          lineHeight: '1.6',
                          color: "#333",
                          margin: 0,
                          padding: '8px',
                          borderRadius: '6px',
                          border: '1px solid transparent',
                          cursor: 'text',
                          transition: 'all 0.2s ease',
                        }}
                        contentEditable={true}
                        suppressContentEditableWarning={true}
                        onBlur={(e) => {
                          const newText = e.target.innerText;
                          if (newText !== entry.text) {
                            handleEditTranscript(index, newText);
                          }
                        }}
                        onFocus={(e) => {
                          e.target.style.border = '1px solid rgba(3, 79, 175, 0.3)';
                          e.target.style.background = 'rgba(255, 255, 255, 0.5)';
                          e.target.style.boxShadow = '0 0 0 2px rgba(3, 79, 175, 0.1)';
                        }}
                        onBlurCapture={(e) => {
                          e.target.style.border = '1px solid transparent';
                          e.target.style.background = 'transparent';
                          e.target.style.boxShadow = 'none';
                        }}
                      >
                        {entry.text}
                      </p>
                      <div style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        fontSize: '12px',
                        color: '#666',
                        opacity: 0,
                        transition: 'opacity 0.2s ease',
                      }}
                      className="edit-hint"
                      >
                        Click to edit
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      <style>
        {`
          ${sharedStyles}

          @keyframes backgroundShift {
            0% { background-position: 0 0; }
            100% { background-position: 100% 100%; }
          }

          @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-15px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0deg); }
          }

          @keyframes shimmer {
            0% { opacity: 0.7; }
            50% { opacity: 0.9; }
            100% { opacity: 0.7; }
          }

          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          /* Custom scrollbar styles */
          .manual-scroll::-webkit-scrollbar {
            width: 10px !important;
            display: block !important;
          }

          .manual-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 4px !important;
          }

          .manual-scroll::-webkit-scrollbar-thumb {
            background: rgba(3, 79, 175, 0.4) !important;
            border-radius: 4px !important;
          }

          .manual-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(3, 79, 175, 0.6) !important;
          }

          /* Hide scrollbar for other containers */
          .conversation-container::-webkit-scrollbar,
          .content-container::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }

          /* Hide scrollbar for IE, Edge and Firefox for other containers */
          .no-scrollbar,
          .conversation-container,
          .content-container {
            -ms-overflow-style: none !important;  /* IE and Edge */
            scrollbar-width: none !important;  /* Firefox */
          }

          /* Enable scrollbar for transcript section in Firefox */
          .manual-scroll {
            scrollbar-width: thin !important;
            scrollbar-color: rgba(3, 79, 175, 0.4) rgba(255, 255, 255, 0.2) !important;
            overflow-y: scroll !important;
          }

          /* Default cursor for text elements */
          h1, h2, h3, h4, h5, h6, p, span, div, section, label {
            cursor: default !important;
          }

          /* Ensure buttons and interactive elements have pointer cursor */
          button, a, .clickable, [role="button"], input[type="file"], input[type="submit"] {
            cursor: pointer !important;
          }

          /* Ensure transcript entries display properly */
          .transcript-entry {
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
          }

          /* Show edit hint on hover */
          .transcript-entry p:hover + .edit-hint,
          .transcript-entry p:focus + .edit-hint {
            opacity: 1 !important;
          }
        `}
      </style>
    </div>
  );
};

export default Conversation;
