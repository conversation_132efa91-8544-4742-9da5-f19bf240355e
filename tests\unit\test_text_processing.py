#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for text file processing in Phase 2
"""

import os
import sys
import time
import argparse

def main():
    print("Testing text file processing...")
    
    # Create a sample text file
    sample_text = """
Speaker 1: Hello everyone, welcome to our meeting.
Speaker 2: Thank you for having us today.
Speaker 1: Let's discuss the agenda for today.
Speaker 2: I think we should start with the project updates.
Speaker 1: Good idea. Let's begin with the marketing team.
Speaker 3: The marketing campaign is going well. We've seen a 20% increase in engagement.
Speaker 1: That's excellent news. What about the development team?
Speaker 4: We're on track with the new features. The beta testing will start next week.
Speaker 2: That's great progress. Any challenges we should be aware of?
Speaker 4: We're facing some issues with the database optimization, but we're working on it.
Speaker 1: Let me know if you need any additional resources.
Speaker 4: Thank you, we'll keep you updated.
Speaker 1: Let's move on to the next item on the agenda.
"""
    
    # Save the sample text to a file
    test_dir = os.path.dirname(os.path.abspath(__file__))
    sample_file_path = os.path.join(test_dir, "sample_transcript.txt")
    
    with open(sample_file_path, "w", encoding="utf-8") as f:
        f.write(sample_text)
    
    print(f"Created sample transcript file at: {sample_file_path}")
    
    # Process the sample file using simple_text_processor.py
    simple_processor_path = os.path.join(test_dir, "simple_text_processor.py")
    
    # Create output directory
    output_dir = os.path.join(test_dir, "test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # Build the command
    cmd = [
        sys.executable,
        simple_processor_path,
        sample_file_path,
        "--language", "english",
        "--title", "Test Meeting",
        "--output_dir", output_dir
    ]
    
    # Run the command
    print(f"Running command: {' '.join(cmd)}")
    start_time = time.time()
    
    import subprocess
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # Print output in real-time
    for line in process.stdout:
        print(line.strip())
    
    # Wait for the process to complete
    process.wait()
    
    # Print any errors
    for line in process.stderr:
        print(f"ERROR: {line.strip()}")
    
    # Check the result
    processing_time = time.time() - start_time
    print(f"Processing completed in {processing_time:.2f} seconds")
    
    # Check if the output files exist
    transcript_dir = os.path.join(output_dir, "transcription")
    minutes_dir = os.path.join(output_dir, "minutes of the meeting")
    
    if os.path.exists(transcript_dir):
        print(f"Transcript directory created: {transcript_dir}")
        transcript_files = os.listdir(transcript_dir)
        print(f"Transcript files: {transcript_files}")
    else:
        print(f"ERROR: Transcript directory not created: {transcript_dir}")
    
    if os.path.exists(minutes_dir):
        print(f"Minutes directory created: {minutes_dir}")
        minutes_files = os.listdir(minutes_dir)
        print(f"Minutes files: {minutes_files}")
        
        # Display the content of the minutes file
        if minutes_files:
            minutes_file_path = os.path.join(minutes_dir, minutes_files[0])
            print(f"\nContent of minutes file: {minutes_file_path}")
            print("-" * 50)
            with open(minutes_file_path, "r", encoding="utf-8") as f:
                print(f.read())
            print("-" * 50)
    else:
        print(f"ERROR: Minutes directory not created: {minutes_dir}")
    
    # Clean up
    try:
        os.remove(sample_file_path)
        print(f"Removed sample file: {sample_file_path}")
    except Exception as e:
        print(f"Error removing sample file: {e}")
    
    print("Test completed.")

if __name__ == "__main__":
    main()
