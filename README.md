# Meeting Assistant

A web application for recording, transcribing, and generating minutes from meetings.

## Features

- Record or upload meeting audio/video
- Transcribe meetings with speaker identification
- Generate meeting minutes automatically
- Export minutes to DOCX format
- Track attendance

## Using the DOCX Template

The application uses a template file (`Template.docx`) for generating DOCX documents. To use your own template:

1. Create a DOCX file with your desired formatting and layout
2. Add the following placeholders in your document where you want the content to appear:
   - `{title}` - The title of the meeting
   - `{dateTime}` - The date and time of the meeting
   - `{attendees}` - List of attendees present at the meeting
   - `{agenda}` - The meeting agenda
   - `{actionItems}` - List of action items from the meeting
   - `{minutes}` - The generated minutes content
3. Save the file as `Template.docx`
4. Place the file in the `public` folder of the application

When a user clicks "Download as DOCX", the application will use this template and replace the placeholders with the actual meeting data.

## Setup

1. Clone the repository
2. Install dependencies with `npm install`
3. Start the development server with `npm start`

## Technologies Used

- React.js
- Node.js
- Express
- TensorFlow
- PyTorch
- BART for summarization
