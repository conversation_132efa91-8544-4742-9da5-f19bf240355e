# Dean's Office AI-Powered Meeting Assistant
## College of Computer Studies - Laguna State Polytechnic University Santa Cruz Campus

A comprehensive AI-powered meeting assistant application built with **Clean Architecture** principles that provides automated transcription and meeting minutes generation using advanced AI technologies.

### 🎓 Academic Project Information
- **Project Type**: Undergraduate Thesis
- **Degree Program**: Bachelor of Science in Computer Science (Major in Intelligent Systems)
- **Institution**: Laguna State Polytechnic University – Santa Cruz Campus
- **College**: College of Computer Studies
- **Academic Year**: 2024-2025

### 👥 Development Team
- **NIEGOS, BARON DENVER D.**
- **BERCADES, JOHN RICHARD L.**
- **VENANCIO, NICKO A.**

### 👩‍🏫 Supervision
- **Dean MIA V. VILLARICA, D.I.T.**

## 🚀 Features

- **Audio/Video Processing**: Convert various audio and video formats to WAV for processing
- **Automated Transcription**: Generate transcripts from audio files using advanced speech recognition
- **Meeting Minutes Generation**: Create structured meeting minutes from transcripts using AI
- **Multi-language Support**: Support for both English and Tagalog languages
- **Document Export**: Export meeting minutes to DOCX format with customizable templates
- **Faculty Attendance Tracking**: Track and manage faculty attendance in meetings
- **Web Interface**: User-friendly React-based web application

## 🏗️ Clean Architecture Structure

This project follows Clean Architecture principles for maintainability and scalability:

```
meeting-assistantv2/
├── 📁 src/                          # Frontend (Presentation Layer)
│   ├── components/                  # React components
│   ├── services/                    # API services
│   ├── utils/                       # Frontend utilities
│   └── styles/                      # Styling
├── 📁 server/                       # API Layer
│   └── server.js                    # Express server
├── 📁 core/                         # Business Logic Layer
│   ├── pipelines/                   # Phase1 & Phase2 processing
│   ├── services/                    # Core business services
│   ├── models/                      # Domain models
│   └── utils/                       # Core utilities
├── 📁 infrastructure/               # Infrastructure Layer
│   ├── database/                    # Database scripts & config
│   ├── storage/                     # File storage & datasets
│   ├── deployment/                  # Deployment scripts
│   └── logs/                        # Application logs
├── 📁 tests/                        # Testing
│   ├── unit/                        # Unit tests
│   ├── integration/                 # Integration tests
│   └── fixtures/                    # Test data
├── 📁 templates/                    # Document templates
├── 📁 config/                       # Configuration files
├── 📁 scripts/                      # Utility scripts
└── 📁 docs/                         # Documentation
    ├── setup/                       # Setup guides
    ├── api/                         # API documentation
    └── architecture/                # Architecture docs
```

## 🛠️ Technology Stack

- **Frontend**: React.js with modern UI components
- **Backend**: Node.js with Express server
- **AI Processing**: Python with PyTorch/TensorFlow for ML models
- **Database**: MySQL for data storage
- **Audio Processing**: FFmpeg for media conversion
- **Document Generation**: Python-docx for DOCX export

## 📋 Prerequisites

- Node.js (v14 or higher)
- Python (v3.8 or higher)
- MySQL Server
- FFmpeg (for audio/video processing)

## 🚀 Quick Start

1. **Clone the repository:**
```bash
git clone https://github.com/yourusername/meeting-assistantv2.git
cd meeting-assistantv2
```

2. **Install dependencies:**
```bash
# Install Node.js dependencies
npm install --prefix config

# Install Python dependencies
pip install -r requirements.txt
```

3. **Download AI Models:** ⚠️ **REQUIRED**
```bash
python core/utils/model_downloader.py ensure
```
*This downloads the BART model (1.5GB) needed for meeting minutes generation*

4. **Set up the database:**
```bash
mysql -u root -p < infrastructure/database/setup_database.sql
```

4. **Configure environment:**
```bash
cp config/.env.example config/.env
# Edit config/.env with your configuration
```

6. **Start the application:**
```bash
# Start backend server
npm run server

# Start frontend (in another terminal)
npm start
```

7. **Access the application at `http://localhost:3000`**

## 📖 Using DOCX Templates

The application uses template files for generating DOCX documents:

1. Templates are located in `templates/` directory
2. Use placeholders in your templates:
   - `{title}` - Meeting title
   - `{dateTime}` - Meeting date and time
   - `{attendees}` - List of attendees
   - `{agenda}` - Meeting agenda
   - `{actionItems}` - Action items
   - `{minutes}` - Generated minutes content

## 📚 Documentation

- [Setup Guide](docs/setup/) - Detailed installation and configuration
- [API Documentation](docs/api/) - API endpoints and usage
- [Architecture Guide](docs/architecture/) - System architecture details

## 🧪 Testing

```bash
# Run unit tests
npm run test:unit

# Run integration tests
npm run test:integration

# Run all tests
npm test
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

We would like to express our sincere gratitude to:

- **Dean Mia V. Villarica, D.I.T.** for her invaluable guidance and supervision throughout this thesis project
- **College of Computer Studies Faculty** at Laguna State Polytechnic University Santa Cruz Campus for their support and expertise
- **Laguna State Polytechnic University** for providing the academic environment and resources necessary for this research
- The **Dean's Office** for their collaboration and providing real-world requirements for this AI-powered meeting assistant

## 📚 Academic Context

This project serves as our undergraduate thesis in partial fulfillment of the requirements for the degree of **Bachelor of Science in Computer Science** with a major in **Intelligent Systems**. The system is specifically designed to address the meeting management needs of the Dean's Office at the College of Computer Studies, demonstrating practical application of AI technologies in educational administration.

## 🏛️ Institution

**Laguna State Polytechnic University – Santa Cruz Campus**
Bubukal, Santa Cruz, Laguna
College of Computer Studies

---

*This project represents the culmination of our undergraduate studies and our contribution to advancing AI applications in educational administration.*
