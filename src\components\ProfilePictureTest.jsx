import React, { useState, useRef } from 'react';
import Avatar from '../assets/user.png';

const ProfilePictureTest = () => {
  const [previewImage, setPreviewImage] = useState(localStorage.getItem('profilePicture') || '');
  const [message, setMessage] = useState('');
  const fileInputRef = useRef(null);

  const handleProfilePictureClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        setMessage('Please upload a valid image file (JPEG, PNG, GIF, WEBP)');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setMessage('Image size should be less than 5MB');
        return;
      }

      setMessage('Processing image...');

      // Compress and resize the image
      const img = new Image();
      const reader = new FileReader();

      reader.onload = (event) => {
        img.src = event.target.result;
        img.onload = () => {
          try {
            // Create a canvas to resize the image
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Calculate new dimensions (max 300x300 pixels)
            const MAX_WIDTH = 300;
            const MAX_HEIGHT = 300;
            let width = img.width;
            let height = img.height;

            if (width > height) {
              if (width > MAX_WIDTH) {
                height *= MAX_WIDTH / width;
                width = MAX_WIDTH;
              }
            } else {
              if (height > MAX_HEIGHT) {
                width *= MAX_HEIGHT / height;
                height = MAX_HEIGHT;
              }
            }

            // Set canvas dimensions and draw the resized image
            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to base64 with reduced quality
            const dataUrl = canvas.toDataURL('image/jpeg', 0.7);

            // Update preview and save to localStorage
            setPreviewImage(dataUrl);
            localStorage.setItem('profilePicture', dataUrl);
            
            // Dispatch event to update sidebar
            const profileUpdateEvent = new CustomEvent('profileUpdated', {
              detail: { profilePicture: dataUrl }
            });
            window.dispatchEvent(profileUpdateEvent);
            
            setMessage('Profile picture updated successfully!');
          } catch (error) {
            console.error('Error processing image:', error);
            setMessage('Error processing image. Please try another file.');
          }
        };
      };

      reader.onerror = () => {
        setMessage('Error reading file. Please try again.');
      };

      reader.readAsDataURL(file);
    }
  };

  const clearProfilePicture = () => {
    localStorage.removeItem('profilePicture');
    setPreviewImage('');
    
    // Dispatch event to update sidebar
    const profileUpdateEvent = new CustomEvent('profileUpdated', {
      detail: { profilePicture: '' }
    });
    window.dispatchEvent(profileUpdateEvent);
    
    setMessage('Profile picture cleared!');
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      padding: '20px',
      maxWidth: '500px',
      margin: '0 auto',
      marginTop: '50px',
      background: 'rgba(255, 255, 255, 0.8)',
      borderRadius: '10px',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
    }}>
      <h2 style={{ color: '#034FAF', marginBottom: '20px' }}>Profile Picture Test</h2>
      
      {message && (
        <div style={{
          padding: '10px 15px',
          borderRadius: '5px',
          background: 'rgba(3, 79, 175, 0.1)',
          marginBottom: '20px',
          width: '100%',
          textAlign: 'center'
        }}>
          {message}
        </div>
      )}
      
      <div
        onClick={handleProfilePictureClick}
        style={{
          width: '150px',
          height: '150px',
          borderRadius: '50%',
          border: '3px solid #034FAF',
          overflow: 'hidden',
          position: 'relative',
          boxShadow: '0 4px 15px rgba(3, 79, 175, 0.3)',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          marginBottom: '20px'
        }}
      >
        <img
          src={previewImage || Avatar}
          alt="Profile"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
          }}
        />
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'rgba(3, 79, 175, 0.7)',
          color: 'white',
          padding: '5px 0',
          fontSize: '12px',
          textAlign: 'center',
          fontWeight: '600',
        }}>
          Change
        </div>
      </div>
      
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileChange}
        accept="image/jpeg,image/png,image/gif,image/webp"
      />
      
      <p style={{ fontSize: '14px', color: '#666', margin: '5px 0 20px', textAlign: 'center' }}>
        Click to upload a profile picture<br />
        <span style={{ fontSize: '12px' }}>(JPEG, PNG, GIF, WEBP, max 5MB)</span>
      </p>
      
      <button
        onClick={clearProfilePicture}
        style={{
          padding: '10px 20px',
          background: 'rgba(244, 67, 54, 0.1)',
          color: '#d32f2f',
          border: '1px solid rgba(244, 67, 54, 0.3)',
          borderRadius: '5px',
          cursor: 'pointer',
          fontWeight: '500',
          marginTop: '10px'
        }}
      >
        Clear Profile Picture
      </button>
      
      <div style={{ marginTop: '30px', textAlign: 'center' }}>
        <h3 style={{ color: '#034FAF', marginBottom: '10px' }}>Debug Information</h3>
        <p style={{ fontSize: '14px', color: '#666' }}>
          Profile picture is {previewImage ? 'set' : 'not set'} in state<br />
          Profile picture is {localStorage.getItem('profilePicture') ? 'set' : 'not set'} in localStorage
        </p>
      </div>
    </div>
  );
};

export default ProfilePictureTest;
