"""
Model Downloader Utility for Dean's Office AI-Powered Meeting Assistant

This utility downloads the required BART model for meeting minutes generation
when it's not present in the local system. This approach keeps the repository
size manageable while ensuring the system has all required components.

Academic Context:
- Project: Dean's Office AI-Powered Meeting Assistant
- Institution: Laguna State Polytechnic University Santa Cruz Campus
- Team: <PERSON>, <PERSON>, <PERSON><PERSON>
- Supervisor: <PERSON>, D.I.T.
"""

import os
import sys
from pathlib import Path
from transformers import BartForConditionalGeneration, BartTokenizer

def download_bart_model():
    """
    Download the BART model for meeting summarization if not present.
    
    Returns:
        bool: True if model is available (downloaded or already exists), False otherwise
    """
    
    # Define model path
    model_path = Path("core/pipelines/Pipeline Phase2/meeting-summary-bart-large")
    model_file = model_path / "model.safetensors"
    
    # Check if model already exists
    if model_file.exists():
        print("✅ BART model already exists locally")
        return True
    
    try:
        print("📥 Downloading BART model for meeting summarization...")
        print("⚠️  This is a large file (1.5GB) and may take several minutes")
        
        # Create directory if it doesn't exist
        model_path.mkdir(parents=True, exist_ok=True)
        
        # Download model and tokenizer
        model_name = "facebook/bart-large-cnn"
        
        print("📥 Downloading tokenizer...")
        tokenizer = BartTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(model_path)
        
        print("📥 Downloading model...")
        model = BartForConditionalGeneration.from_pretrained(model_name)
        model.save_pretrained(model_path)
        
        print("✅ BART model downloaded successfully!")
        print(f"📁 Model saved to: {model_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error downloading BART model: {str(e)}")
        print("🔧 Please check your internet connection and try again")
        return False

def verify_model():
    """
    Verify that the BART model is properly installed and accessible.
    
    Returns:
        bool: True if model can be loaded, False otherwise
    """
    
    model_path = Path("core/pipelines/Pipeline Phase2/meeting-summary-bart-large")
    
    try:
        # Try to load the model
        tokenizer = BartTokenizer.from_pretrained(model_path)
        model = BartForConditionalGeneration.from_pretrained(model_path)
        
        print("✅ BART model verification successful!")
        return True
        
    except Exception as e:
        print(f"❌ Model verification failed: {str(e)}")
        return False

def ensure_model_available():
    """
    Ensure the BART model is available for use.
    Downloads if necessary and verifies functionality.
    
    Returns:
        bool: True if model is ready for use, False otherwise
    """
    
    print("🔍 Checking BART model availability...")
    
    # First, try to verify existing model
    if verify_model():
        return True
    
    # If verification fails, try to download
    print("📥 Model not found or corrupted. Attempting download...")
    
    if download_bart_model():
        # Verify the downloaded model
        return verify_model()
    
    return False

if __name__ == "__main__":
    """
    Command-line interface for model management
    """
    
    print("🎓 Dean's Office AI-Powered Meeting Assistant")
    print("📚 BART Model Management Utility")
    print("🏛️  Laguna State Polytechnic University Santa Cruz Campus")
    print("-" * 60)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "download":
            success = download_bart_model()
            sys.exit(0 if success else 1)
            
        elif command == "verify":
            success = verify_model()
            sys.exit(0 if success else 1)
            
        elif command == "ensure":
            success = ensure_model_available()
            sys.exit(0 if success else 1)
            
        else:
            print("❌ Unknown command. Use: download, verify, or ensure")
            sys.exit(1)
    else:
        # Default behavior: ensure model is available
        success = ensure_model_available()
        sys.exit(0 if success else 1)
