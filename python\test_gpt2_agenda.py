"""
Test script for GPT-2 agenda identification
"""
import os
import sys
import time
from datetime import timedelta

# Force CPU usage for testing
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

# Add the Pipeline Phase2 directory to the path
sys.path.append('Pipeline Phase2')

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    """Test GPT-2 agenda identification."""
    start_time = time.time()
    
    print("Testing GPT-2 agenda identification...")
    
    # Import the agenda identification function
    try:
        from gpt2_agenda import process_transcript_for_agenda
        print("Successfully imported GPT-2 agenda identification")
    except ImportError as e:
        print(f"Error importing GPT-2 agenda identification: {e}")
        return
    
    # Create a sample transcript
    sample_transcript = """
    Speaker 1: Good morning everyone. Let's start with the first agenda item: Project Updates.
    Speaker 2: The mobile app development is on track. We've completed the UI design and started backend integration.
    Speaker 1: Great. Any challenges or blockers?
    Speaker 2: No major issues, but we might need additional QA resources next month.
    
    Speaker 1: Moving on to our next topic, Budget Review.
    Speaker 3: We're currently under budget by about 5%. The cost-saving measures implemented last quarter are working well.
    Speaker 1: That's excellent news. Any areas where we need to allocate additional funds?
    Speaker 3: The marketing team has requested an increase for the Q4 campaign.
    
    Speaker 1: Now let's discuss the Faculty Development program.
    Speaker 4: We've scheduled three training sessions for next month. The focus will be on new teaching methodologies.
    Speaker 2: Will these be mandatory for all faculty members?
    Speaker 4: No, but we strongly encourage participation, especially for junior faculty.
    """
    
    print("\nSample transcript:")
    print("-" * 50)
    print(sample_transcript)
    print("-" * 50)
    
    # Test agenda identification
    print("\nIdentifying agenda items...")
    results = process_transcript_for_agenda(sample_transcript)
    
    if results:
        print("\nIdentified Agenda Items:")
        print("-" * 50)
        for segment, category in results:
            print(f"Category: {category}")
            print(f"Segment: {segment[:100]}...")
            print("-" * 50)
    else:
        print("Agenda identification failed or is disabled")
    
    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nProcessing completed in {format_time(processing_time)}")

if __name__ == "__main__":
    main()
