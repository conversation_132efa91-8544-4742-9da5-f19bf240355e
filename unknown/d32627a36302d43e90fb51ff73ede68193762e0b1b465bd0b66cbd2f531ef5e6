"""
Test script for the transcript enhancement feature in Phase2V2.py
"""
import os
import sys
import time
from datetime import timedelta

# Add the Pipeline Phase2 directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Pipeline Phase2'))

# Force CPU usage for PyTorch to avoid GPU memory issues during testing
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def main():
    """Test the transcript enhancement feature."""
    start_time = time.time()
    
    print("Testing transcript enhancement feature...")
    
    # Import the enhancement function
    try:
        from Phase2V2 import enhance_transcript_with_model, basic_transcript_cleaning
        print("Successfully imported enhancement functions")
    except ImportError as e:
        print(f"Error importing enhancement functions: {e}")
        return
    
    # Create a sample transcript
    sample_transcript = """
    Speaker 1: Good morning everyone um I would like to start the meeting by discussing the project timeline.
    Speaker 2: Yes, I think we should uh focus on the deliverables for next week.
    Speaker 1: I agree. We need to complete the first phase by Friday.
    Speaker 2: What about the budget concerns that were raised in the last meeting?
    Speaker 1: We will address those after we finalize the timeline.
    """
    
    print("\nOriginal transcript:")
    print("-" * 50)
    print(sample_transcript)
    print("-" * 50)
    
    # Test basic cleaning
    print("\nTesting basic transcript cleaning...")
    basic_cleaned = basic_transcript_cleaning(sample_transcript)
    print("\nBasic cleaned transcript:")
    print("-" * 50)
    print(basic_cleaned)
    print("-" * 50)
    
    # Test full enhancement
    print("\nTesting full transcript enhancement...")
    try:
        enhanced = enhance_transcript_with_model(sample_transcript)
        print("\nEnhanced transcript:")
        print("-" * 50)
        print(enhanced)
        print("-" * 50)
        print("Enhancement successful!")
    except Exception as e:
        print(f"Error during enhancement: {e}")
        import traceback
        print(traceback.format_exc())
        print("Enhancement failed!")
    
    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nProcessing completed in {format_time(processing_time)}")

if __name__ == "__main__":
    main()
