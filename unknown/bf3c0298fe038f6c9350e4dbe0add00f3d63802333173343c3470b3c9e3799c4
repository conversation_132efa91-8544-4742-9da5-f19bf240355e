# How to Change MySQL Port in XAMPP

Follow these steps to change the MySQL port from the default 3306 to 3307:

## Step 1: Edit my.ini

1. In XAMPP Control Panel, click on "Config" next to MySQL
2. Select "my.ini"
3. Find these lines (use Ctrl+F to search):

```
port=3306
```

4. Change all occurrences to:

```
port=3307
```

   There should be at least two occurrences - one in the [mysqld] section and one in the [client] section.

## Step 2: Restart MySQL

1. Stop MySQL if it's running
2. Start MySQL again from XAMPP Control Panel

## Step 3: Verify the Port Change

1. Open a command prompt
2. Navigate to your MySQL bin directory:
   ```
   cd C:\xampp\mysql\bin
   ```
3. Connect to MySQL using the new port:
   ```
   mysql -u root -P 3307
   ```
4. If successful, you should see the MySQL prompt:
   ```
   mysql>
   ```

## Step 4: Update Your Application

Make sure your application's .env file has the correct port:

```
DB_PORT=3307
```

## Troubleshooting

If MySQL fails to start after changing the port:

1. Check the MySQL error log in XAMPP Control Panel
2. Make sure no other application is using port 3307
3. Try a different port (e.g., 3308)
4. Restore the original my.ini file from my.ini.bak if needed
