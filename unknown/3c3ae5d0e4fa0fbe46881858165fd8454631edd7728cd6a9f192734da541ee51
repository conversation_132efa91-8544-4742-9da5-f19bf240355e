const fs = require('fs');
const path = require('path');

// Read the app.js file
const appJsPath = path.join(__dirname, 'server', 'app.js');
let content = fs.readFileSync(appJsPath, 'utf8');

// Fix the SSE response format
content = content.replace(
  `res.write(\`data: \${JSON.stringify({ type: 'connected', jobId })}}\n\n\`);`,
  `res.write(\`data: \${JSON.stringify({ type: 'connected', jobId })}\n\n\`);`
);

// Write the fixed content back to the file
fs.writeFileSync(appJsPath, content, 'utf8');

console.log('Fixed SSE response format in app.js');
