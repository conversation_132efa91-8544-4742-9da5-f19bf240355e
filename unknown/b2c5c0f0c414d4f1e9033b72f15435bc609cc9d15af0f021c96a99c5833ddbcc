#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for verifying attendance data is properly saved when uploading text files
"""

import os
import sys
import time
import json
import argparse

def main():
    print("Testing attendance data handling for text files...")
    
    # Create a sample text file
    sample_text = """
Speaker 1: Hello everyone, welcome to our meeting.
Speaker 2: Thank you for having us today.
Speaker 1: Let's discuss the agenda for today.
Speaker 2: I think we should start with the project updates.
Speaker 1: Good idea. Let's begin with the marketing team.
Speaker 3: The marketing campaign is going well. We've seen a 20% increase in engagement.
Speaker 1: That's excellent news. What about the development team?
Speaker 4: We're on track with the new features. The beta testing will start next week.
Speaker 2: That's great progress. Any challenges we should be aware of?
Speaker 4: We're facing some issues with the database optimization, but we're working on it.
Speaker 1: Let me know if you need any additional resources.
Speaker 4: Thank you, we'll keep you updated.
Speaker 1: Let's move on to the next item on the agenda.
"""
    
    # Create sample attendance data
    attendance_data = [
        {"facultyId": 1, "status": "present"},
        {"facultyId": 2, "status": "present"},
        {"facultyId": 3, "status": "absent"},
        {"facultyId": 4, "status": "present"},
        {"facultyId": 5, "status": "absent"}
    ]
    
    # Save the sample text to a file
    test_dir = os.path.dirname(os.path.abspath(__file__))
    sample_file_path = os.path.join(test_dir, "sample_transcript_with_attendance.txt")
    
    with open(sample_file_path, "w", encoding="utf-8") as f:
        f.write(sample_text)
    
    print(f"Created sample transcript file at: {sample_file_path}")
    
    # Process the sample file using simple_text_processor.py
    simple_processor_path = os.path.join(test_dir, "simple_text_processor.py")
    
    # Create output directory
    output_dir = os.path.join(test_dir, "test_output_attendance")
    os.makedirs(output_dir, exist_ok=True)
    
    # Build the command
    cmd = [
        sys.executable,
        simple_processor_path,
        sample_file_path,
        "--language", "english",
        "--title", "Test Meeting with Attendance",
        "--output_dir", output_dir,
        "--faculty_attendance", json.dumps(attendance_data)
    ]
    
    # Run the command
    print(f"Running command: {' '.join(cmd)}")
    start_time = time.time()
    
    import subprocess
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    # Print output in real-time
    for line in process.stdout:
        print(line.strip())
    
    # Wait for the process to complete
    process.wait()
    
    # Print any errors
    for line in process.stderr:
        print(f"ERROR: {line.strip()}")
    
    # Check the result
    processing_time = time.time() - start_time
    print(f"Processing completed in {processing_time:.2f} seconds")
    
    # Look for the JSON output to verify attendance data
    json_output = None
    json_start_marker = "===JSON_OUTPUT_START==="
    json_end_marker = "===JSON_OUTPUT_END==="
    
    # Read the process output again to find the JSON
    process_output = subprocess.check_output(cmd, universal_newlines=True)
    
    # Extract JSON from the output
    start_index = process_output.find(json_start_marker)
    end_index = process_output.find(json_end_marker)
    
    if start_index >= 0 and end_index > start_index:
        json_text = process_output[start_index + len(json_start_marker):end_index].strip()
        try:
            json_output = json.loads(json_text)
            print("Successfully parsed JSON output")
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON: {e}")
    else:
        print("Could not find JSON output markers in the process output")
    
    # Verify attendance data in the output
    if json_output:
        # Check if attendance data is included in the result
        if "result" in json_output and "data" in json_output["result"]:
            result_data = json_output["result"]["data"]
            
            if "attendance" in result_data:
                attendance = result_data["attendance"]
                print(f"\nAttendance data found in the output: {len(attendance)} records")
                print("Attendance data:")
                for record in attendance:
                    print(f"  Faculty ID: {record['facultyId']}, Status: {record['status']}")
                
                # Verify the attendance data matches what we sent
                matches = True
                if len(attendance) != len(attendance_data):
                    matches = False
                    print(f"ERROR: Attendance record count mismatch. Expected {len(attendance_data)}, got {len(attendance)}")
                else:
                    for i, record in enumerate(attendance):
                        expected = attendance_data[i]
                        if record["facultyId"] != expected["facultyId"] or record["status"] != expected["status"]:
                            matches = False
                            print(f"ERROR: Attendance record mismatch at index {i}:")
                            print(f"  Expected: {expected}")
                            print(f"  Got: {record}")
                
                if matches:
                    print("\nSUCCESS: Attendance data was correctly saved and returned!")
                else:
                    print("\nFAILURE: Attendance data does not match what was sent!")
            else:
                print("ERROR: No attendance data found in the output")
        else:
            print("ERROR: Invalid JSON structure in the output")
    
    # Clean up
    try:
        os.remove(sample_file_path)
        print(f"Removed sample file: {sample_file_path}")
    except Exception as e:
        print(f"Error removing sample file: {e}")
    
    print("Test completed.")

if __name__ == "__main__":
    main()
