#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for DOCX generation
"""

import os
import json
import sys
import subprocess
import time

def main():
    """
    Main function to test DOCX generation
    """
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Path to the template file
    template_path = os.path.join(os.path.dirname(current_dir), 'Template.docx')

    # Create a temporary directory for processing
    temp_dir = os.path.join(current_dir, 'temp')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    # Create a temporary JSON file with test data
    data_path = os.path.join(temp_dir, 'test_data.json')
    output_path = os.path.join(temp_dir, f'test_output_{int(time.time())}.docx')

    # Test data
    test_data = {
        'title': 'Test Minutes of the Meeting',
        'dateTime': '2025-04-24 14:30:00',
        'attendees': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'content': 'This is a test content for the minutes.\nIt has multiple lines.\nEach line should be a separate paragraph.',
        'actionItems': ['Action item 1', 'Action item 2', 'Action item 3']
    }

    # Write the test data to the JSON file
    with open(data_path, 'w', encoding='utf-8') as f:
        json.dump(test_data, f)

    # Check if the template file exists
    if not os.path.exists(template_path):
        print(f"Template file not found: {template_path}")
        return 1

    # Run the generate_docx.py script
    cmd = [
        sys.executable,
        os.path.join(current_dir, 'generate_docx.py'),
        template_path,
        data_path,
        output_path
    ]

    print(f"Running command: {' '.join(cmd)}")
    print(f"Template path: {template_path}")
    print(f"Data path: {data_path}")
    print(f"Output path: {output_path}")

    try:
        result = subprocess.run(cmd, capture_output=True, text=True)

        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")

        if result.returncode == 0:
            print(f"DOCX file generated successfully: {output_path}")
            if os.path.exists(output_path):
                print(f"Output file size: {os.path.getsize(output_path)} bytes")
            else:
                print("Output file not found!")
        else:
            print("DOCX generation failed!")

        return result.returncode
    except Exception as e:
        print(f"Error running generate_docx.py: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
