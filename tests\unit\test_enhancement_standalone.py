"""
Standalone test script for transcript enhancement
"""
import os
import sys
import time
from datetime import timedelta

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

# Create a sample transcript
sample_transcript = """
Speaker 1: Good morning everyone um I would like to start the meeting by discussing the project timeline.
Speaker 2: Yes, I think we should uh focus on the deliverables for next week.
Speaker 1: I agree. We need to complete the first phase by Friday.
Speaker 2: What about the budget concerns that were raised in the last meeting?
Speaker 1: We will address those after we finalize the timeline.
"""

# Define a basic transcript cleaning function
def basic_transcript_cleaning(transcript):
    """
    Apply basic cleaning to a transcript using regex patterns.
    """
    # Remove common filler words
    cleaned = transcript.replace("um", "").replace("uh", "")
    
    # Fix spacing issues
    cleaned = cleaned.replace("  ", " ")
    
    return cleaned

# Define a simple enhancement function
def enhance_transcript(transcript):
    """
    Enhance a transcript with basic cleaning and some improvements.
    """
    start_time = time.time()
    
    # Apply basic cleaning first
    cleaned_transcript = basic_transcript_cleaning(transcript)
    
    # Process each line separately to preserve speaker attributions
    lines = cleaned_transcript.split('\n')
    enhanced_lines = []
    
    for line in lines:
        if not line.strip():
            enhanced_lines.append(line)
            continue
            
        # Check if this is a speaker attribution line
        if ':' in line:
            speaker, content = line.split(':', 1)
            
            # Only enhance the content, not the speaker attribution
            if content.strip():
                # Simple enhancement: capitalize first letter of sentences
                sentences = content.split('.')
                enhanced_sentences = []
                
                for sentence in sentences:
                    if sentence.strip():
                        # Capitalize first letter
                        enhanced = sentence.strip()
                        if enhanced:
                            enhanced = enhanced[0].upper() + enhanced[1:]
                        enhanced_sentences.append(enhanced)
                
                # Join sentences back together
                enhanced_content = '. '.join(enhanced_sentences)
                if not enhanced_content.endswith('.'):
                    enhanced_content += '.'
                    
                # Combine speaker with enhanced content
                enhanced_line = f"{speaker}: {enhanced_content}"
            else:
                enhanced_line = line
        else:
            # Not a speaker line, just pass through
            enhanced_line = line
            
        enhanced_lines.append(enhanced_line)
    
    # Combine enhanced lines
    enhanced_transcript = '\n'.join(enhanced_lines)
    
    elapsed = time.time() - start_time
    print(f"Enhancement completed in {format_time(elapsed)}")
    
    return enhanced_transcript

def main():
    """Test the transcript enhancement."""
    start_time = time.time()
    
    print("Testing transcript enhancement...")
    
    print("\nOriginal transcript:")
    print("-" * 50)
    print(sample_transcript)
    print("-" * 50)
    
    # Test basic cleaning
    print("\nTesting basic transcript cleaning...")
    basic_cleaned = basic_transcript_cleaning(sample_transcript)
    print("\nBasic cleaned transcript:")
    print("-" * 50)
    print(basic_cleaned)
    print("-" * 50)
    
    # Test enhancement
    print("\nTesting transcript enhancement...")
    enhanced = enhance_transcript(sample_transcript)
    print("\nEnhanced transcript:")
    print("-" * 50)
    print(enhanced)
    print("-" * 50)
    
    # Print processing time
    processing_time = time.time() - start_time
    print(f"\nProcessing completed in {format_time(processing_time)}")
    print("Test completed successfully!")

if __name__ == "__main__":
    main()
