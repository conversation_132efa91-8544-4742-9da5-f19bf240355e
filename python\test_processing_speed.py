"""
Performance testing utility for comparing processing speeds.
This script compares the original processing pipeline with the optimized version.
"""

import os
import sys
import time
import json
import argparse
from datetime import timedelta

def format_time(seconds):
    """Format seconds into a human-readable time string."""
    return str(timedelta(seconds=int(seconds)))

def test_processing_speed(input_file, use_optimized=True, title="Test Meeting", description="Test Description"):
    """
    Test processing speed for a given audio/video file.
    
    Args:
        input_file (str): Path to input file
        use_optimized (bool): Whether to use the optimized processing pipeline
        title (str): Title for the meeting
        description (str): Description for the meeting
        
    Returns:
        dict: Processing results and timing information
    """
    # Record start time
    start_time = time.time()
    
    # Import appropriate processing module
    if use_optimized:
        print("Using optimized processing pipeline (fast_process.py)")
        from fast_process import process_file
    else:
        print("Using standard processing pipeline (direct_process.py)")
        from direct_process import process_file
    
    # Process the file
    result = process_file(input_file, title, description, "test_user")
    
    # Calculate total processing time
    total_time = time.time() - start_time
    
    # Add timing information to result
    result["benchmark"] = {
        "total_time": total_time,
        "total_time_formatted": format_time(total_time),
        "pipeline": "optimized" if use_optimized else "standard"
    }
    
    return result

def compare_processing_speeds(input_file, title="Test Meeting", description="Test Description"):
    """
    Compare processing speeds between standard and optimized pipelines.
    
    Args:
        input_file (str): Path to input file
        title (str): Title for the meeting
        description (str): Description for the meeting
        
    Returns:
        dict: Comparison results
    """
    print(f"\n=== PERFORMANCE COMPARISON FOR: {input_file} ===\n")
    
    # Test standard pipeline
    print("\n--- Testing Standard Pipeline ---\n")
    standard_result = test_processing_speed(input_file, use_optimized=False, title=title, description=description)
    standard_time = standard_result["benchmark"]["total_time"]
    
    # Wait a bit to let system resources settle
    print("\nWaiting 5 seconds before testing optimized pipeline...\n")
    time.sleep(5)
    
    # Test optimized pipeline
    print("\n--- Testing Optimized Pipeline ---\n")
    optimized_result = test_processing_speed(input_file, use_optimized=True, title=title, description=description)
    optimized_time = optimized_result["benchmark"]["total_time"]
    
    # Calculate improvement
    time_diff = standard_time - optimized_time
    percentage_improvement = (time_diff / standard_time) * 100 if standard_time > 0 else 0
    
    # Prepare comparison result
    comparison = {
        "standard": {
            "total_time": standard_time,
            "total_time_formatted": format_time(standard_time)
        },
        "optimized": {
            "total_time": optimized_time,
            "total_time_formatted": format_time(optimized_time)
        },
        "improvement": {
            "time_saved": time_diff,
            "time_saved_formatted": format_time(time_diff),
            "percentage": percentage_improvement
        },
        "file_info": {
            "path": input_file,
            "size_mb": os.path.getsize(input_file) / (1024 * 1024),
            "title": title
        }
    }
    
    # Print comparison results
    print("\n=== PERFORMANCE COMPARISON RESULTS ===\n")
    print(f"File: {input_file}")
    print(f"Size: {comparison['file_info']['size_mb']:.2f} MB\n")
    print(f"Standard Pipeline: {comparison['standard']['total_time_formatted']} ({comparison['standard']['total_time']:.2f} seconds)")
    print(f"Optimized Pipeline: {comparison['optimized']['total_time_formatted']} ({comparison['optimized']['total_time']:.2f} seconds)")
    print(f"\nImprovement: {comparison['improvement']['time_saved_formatted']} ({comparison['improvement']['percentage']:.2f}%)")
    
    return comparison

def main():
    """Main function to run performance tests."""
    parser = argparse.ArgumentParser(description="Test and compare processing speeds for audio/video files")
    parser.add_argument("input_file", help="Path to input audio/video file")
    parser.add_argument("--title", default="Test Meeting", help="Title for the meeting")
    parser.add_argument("--description", default="Test Description", help="Description for the meeting")
    parser.add_argument("--optimized-only", action="store_true", help="Only test the optimized pipeline")
    parser.add_argument("--standard-only", action="store_true", help="Only test the standard pipeline")
    parser.add_argument("--output", help="Path to save comparison results as JSON")
    
    args = parser.parse_args()
    
    # Verify file exists
    if not os.path.exists(args.input_file):
        print(f"Error: File not found: {args.input_file}")
        return 1
    
    # Run appropriate test
    if args.optimized_only:
        result = test_processing_speed(args.input_file, use_optimized=True, title=args.title, description=args.description)
        print(f"\nOptimized Pipeline: {result['benchmark']['total_time_formatted']} ({result['benchmark']['total_time']:.2f} seconds)")
    elif args.standard_only:
        result = test_processing_speed(args.input_file, use_optimized=False, title=args.title, description=args.description)
        print(f"\nStandard Pipeline: {result['benchmark']['total_time_formatted']} ({result['benchmark']['total_time']:.2f} seconds)")
    else:
        result = compare_processing_speeds(args.input_file, title=args.title, description=args.description)
    
    # Save results if output path is provided
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2)
        print(f"\nResults saved to: {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
