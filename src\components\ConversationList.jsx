import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Sidebar from './Sidebar';
import backIcon from '../assets/back.png';
import { synchronizeAll } from '../utils/syncService';

const ConversationList = () => {
    const navigate = useNavigate();
    const [meetingMinutes, setMeetingMinutes] = useState([]);

    // Table styles
    const tableHeaderStyle = {
        textAlign: 'left',
        padding: '15px 20px',
        color: '#034FAF',
        fontWeight: '700',
        fontSize: '14px',
        letterSpacing: '0.5px',
        borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
    };

    const tableDataStyle = {
        padding: '15px 20px',
        color: '#333',
    };

    // Load transcripts from localStorage on component mount
    useEffect(() => {
        console.log('ConversationList: Loading transcripts from localStorage');

        // First, synchronize all data to ensure consistency
        synchronizeAll();

        // Then get the transcripts from localStorage
        const savedTranscripts = JSON.parse(localStorage.getItem('savedTranscripts') || '[]');
        console.log(`ConversationList: Found ${savedTranscripts.length} transcripts after synchronization`);

        if (savedTranscripts.length === 0) {
            console.log('ConversationList: No saved transcripts, showing empty state');
            // Show empty state - no demo data
            setMeetingMinutes([]);
        } else {
            console.log('ConversationList: Formatting saved transcripts for display');
            // Format dates for display
            const formattedTranscripts = savedTranscripts.map(transcript => ({
                ...transcript,
                createdAt: formatDateTime(transcript.createdAt),
                updatedAt: formatDateTime(transcript.updatedAt)
            }));
            setMeetingMinutes(formattedTranscripts);
        }
    }, []);

    // Helper function to format date and time
    const formatDateTime = (timestamp) => {
        if (!timestamp) return '';

        try {
            const date = new Date(timestamp);
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        } catch (e) {
            console.error('Error formatting date:', e);
            return timestamp;
        }
    };

    const handleRowClick = (id) => {
        navigate(`/transcription/${id}`);
    };

    const handleDelete = (e, id, title) => {
        e.stopPropagation();
        // Add confirmation before deletion
        if (window.confirm(`Are you sure you want to delete "${title}"?`)) {
            console.log(`ConversationList: Deleting ${title} with ID ${id}`);

            // Get current saved transcripts
            const savedTranscripts = JSON.parse(localStorage.getItem('savedTranscripts') || '[]');

            // Filter out the transcript to delete
            const updatedTranscripts = savedTranscripts.filter(transcript => transcript.id !== id);

            // Save back to localStorage
            localStorage.setItem('savedTranscripts', JSON.stringify(updatedTranscripts));

            // Also remove from currentTranscript if it's the one being deleted
            try {
                const currentTranscript = JSON.parse(localStorage.getItem('currentTranscript') || '{}');
                if (currentTranscript.id === id) {
                    localStorage.removeItem('currentTranscript');
                    console.log('ConversationList: Removed deleted transcript from currentTranscript');
                }
            } catch (e) {
                console.error('ConversationList: Error checking currentTranscript:', e);
            }

            // Update the UI
            const formattedTranscripts = updatedTranscripts.map(transcript => ({
                ...transcript,
                createdAt: formatDateTime(transcript.createdAt),
                updatedAt: formatDateTime(transcript.updatedAt)
            }));

            // Update the UI with the formatted transcripts (or empty array if none left)
            console.log(`ConversationList: ${formattedTranscripts.length} transcripts left after deletion`);
            setMeetingMinutes(formattedTranscripts);

            // Re-synchronize data to ensure consistency
            synchronizeAll();
        }
    };

    return (
        <div style={{
            display: 'flex',
            minHeight: '100vh',
            fontFamily: 'Montserrat, sans-serif',
            position: 'relative',
            overflow: 'hidden'
        }}>
            {/* Background Pattern */}
            <div className="background-pattern" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `
                    linear-gradient(45deg, rgba(3, 79, 175, 0.1), rgba(87, 215, 226, 0.1)),
                    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23034FAF' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
                `,
                animation: 'backgroundShift 30s linear infinite',
                zIndex: 0,
            }} />

            {/* Gradient Overlay */}
            <div className="gradient-overlay" style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(3, 79, 175, 0.25) 100%)',
                zIndex: 1,
            }} />

            {/* Decorative Elements */}
            <div className="decorative-circle circle-1" style={{
                position: 'absolute',
                width: '400px',
                height: '400px',
                borderRadius: '50%',
                background: 'radial-gradient(circle, rgba(3, 79, 175, 0.25) 0%, rgba(87, 215, 226, 0.12) 70%)',
                top: '-150px',
                right: '5%',
                animation: 'float 15s ease-in-out infinite, shimmer 8s infinite',
                zIndex: 1,
                opacity: 0.7,
                filter: 'blur(80px)',
            }} />

            <div className="decorative-circle circle-2" style={{
                position: 'absolute',
                width: '300px',
                height: '300px',
                borderRadius: '50%',
                background: 'radial-gradient(circle, rgba(87, 215, 226, 0.25) 0%, rgba(3, 79, 175, 0.12) 70%)',
                bottom: '5%',
                left: '25%',
                animation: 'float 12s ease-in-out infinite reverse, shimmer 10s infinite 2s',
                zIndex: 1,
                opacity: 0.7,
                filter: 'blur(80px)',
            }} />

            <Sidebar style={{ position: 'relative', zIndex: 2 }} />

            <div style={{
                flex: 1,
                padding: '30px',
                height: '100vh',
                overflow: 'auto',
                position: 'relative',
                zIndex: 2,
                msOverflowStyle: 'none',
                scrollbarWidth: 'none',
            }}>
                <div className="content-card">
                    {/* Header */}
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: '30px',
                        gap: '20px',
                        background: 'rgba(255, 255, 255, 0.15)',
                        padding: '15px 25px',
                        borderRadius: '15px',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.25)',
                        transition: 'all 0.3s ease',
                        animation: 'fadeIn 0.8s ease-out',
                    }}>
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            padding: '12px',
                            borderRadius: '50%',
                            cursor: 'pointer',
                            transition: 'all 0.3s ease',
                        }}
                        onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'scale(1.05)';
                            e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
                        }}
                        onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'scale(1)';
                            e.currentTarget.style.background = 'transparent';
                        }}
                        onClick={() => window.history.back()}
                        >
                            <img
                                src={backIcon}
                                alt="back"
                                style={{
                                    width: '28px',
                                    height: '28px',
                                    transition: 'transform 0.3s ease',
                                    cursor: 'pointer',
                                }}
                            />
                        </div>
                        <h1 style={{
                            margin: 0,
                            fontSize: "24px",
                            fontWeight: "900",
                            color: '#034FAF',
                            letterSpacing: '0.5px',
                            cursor: 'default',
                            textShadow: '0 1px 3px rgba(255, 255, 255, 0.7)',
                        }}>TRANSCRIPTIONS</h1>

                        <div style={{
                            marginLeft: 'auto',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '10px'
                        }}>
                            {/* View All Files Button */}
                            <button
                                style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    padding: '10px 15px',
                                    background: 'rgba(3, 79, 175, 0.1)',
                                    border: 'none',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    transition: 'all 0.3s ease',
                                    color: '#034FAF',
                                    fontWeight: '600',
                                    fontSize: '14px',
                                }}
                                onClick={() => navigate('/all-transcripts')}
                                onMouseEnter={(e) => {
                                    e.currentTarget.style.background = 'rgba(3, 79, 175, 0.2)';
                                }}
                                onMouseLeave={(e) => {
                                    e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
                                }}
                                title="View All Transcript Files"
                            >
                                View All Files
                            </button>

                            {/* Clear All Button */}
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: '8px',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease',
                                background: 'rgba(220, 38, 38, 0.1)',
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.transform = 'scale(1.05)';
                                e.currentTarget.style.background = 'rgba(220, 38, 38, 0.2)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'scale(1)';
                                e.currentTarget.style.background = 'rgba(220, 38, 38, 0.1)';
                            }}
                            onClick={() => {
                                // Add confirmation before clearing all
                                if (window.confirm('Are you sure you want to delete ALL transcripts? This action cannot be undone.')) {
                                    console.log('ConversationList: Clearing all transcripts');

                                    // Clear all transcripts from localStorage
                                    localStorage.removeItem('savedTranscripts');
                                    localStorage.removeItem('currentTranscript');

                                    // Also clear minutes for consistency
                                    localStorage.removeItem('savedMinutes');
                                    localStorage.removeItem('currentMinute');

                                    // Clear faculty attendance records
                                    localStorage.removeItem('facultyAttendanceRecords');

                                    // Update the UI with empty state
                                    setMeetingMinutes([]);

                                    // Force a refresh of the minutes list
                                    localStorage.setItem('minutesLastSync', Date.now().toString());

                                    // Show success message
                                    alert('All transcripts have been cleared successfully.');
                                }
                            }}
                            title="Clear All Transcripts"
                            >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3 6H5H21" stroke="#DC2626" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#DC2626" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>

                            {/* Refresh Button */}
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                padding: '8px',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                transition: 'all 0.3s ease',
                                background: 'rgba(3, 79, 175, 0.1)',
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.transform = 'scale(1.05)';
                                e.currentTarget.style.background = 'rgba(3, 79, 175, 0.2)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'scale(1)';
                                e.currentTarget.style.background = 'rgba(3, 79, 175, 0.1)';
                            }}
                            onClick={() => {
                                console.log('ConversationList: Refreshing data');
                                synchronizeAll();

                                // Get updated transcripts
                                const savedTranscripts = JSON.parse(localStorage.getItem('savedTranscripts') || '[]');

                                // Format and update UI
                                const formattedTranscripts = savedTranscripts.map(transcript => ({
                                    ...transcript,
                                    createdAt: formatDateTime(transcript.createdAt),
                                    updatedAt: formatDateTime(transcript.updatedAt)
                                }));

                                if (formattedTranscripts.length > 0) {
                                    setMeetingMinutes(formattedTranscripts);
                                } else {
                                    setMeetingMinutes([]);
                                }
                            }}
                            title="Refresh Transcriptions List"
                            >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 4V10H7" stroke="#034FAF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M23 20V14H17" stroke="#034FAF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    <path d="M20.49 9.00001C19.9828 7.56645 19.1209 6.28161 17.9845 5.27884C16.8482 4.27607 15.4745 3.58374 13.9917 3.26763C12.5089 2.95151 10.9652 3.02252 9.51845 3.47383C8.07173 3.92513 6.76634 4.74145 5.73001 5.83L1 10M23 14L18.27 18.17C17.2337 19.2586 15.9283 20.0749 14.4816 20.5262C13.0349 20.9775 11.4911 21.0485 10.0083 20.7324C8.52547 20.4163 7.1518 19.724 6.01547 18.7212C4.87913 17.7184 4.01717 16.4336 3.51001 15" stroke="#034FAF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    {/* Table Container */}
                    <div style={{
                        background: 'rgba(255, 255, 255, 0.15)',
                        borderRadius: '15px',
                        padding: '30px',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        backdropFilter: 'blur(10px)',
                        WebkitBackdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255, 255, 255, 0.25)',
                        animation: 'fadeIn 0.8s ease-out',
                    }}>
                        {meetingMinutes.length === 0 ? (
                            // Empty state
                            <div style={{
                                textAlign: 'center',
                                padding: '40px 20px',
                                color: '#566573',
                                animation: 'fadeIn 0.8s ease-out',
                            }}>
                                <div style={{
                                    width: '80px',
                                    height: '80px',
                                    margin: '0 auto 20px',
                                    background: 'rgba(3, 79, 175, 0.1)',
                                    borderRadius: '50%',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}>
                                    <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="#034FAF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </div>
                                <h3 style={{
                                    fontSize: '20px',
                                    fontWeight: '700',
                                    color: '#034FAF',
                                    margin: '0 0 10px',
                                }}>No Transcripts Found</h3>
                                <p style={{
                                    fontSize: '16px',
                                    lineHeight: '1.6',
                                    maxWidth: '500px',
                                    margin: '0 auto 30px',
                                }}>
                                    You haven't uploaded any meeting recordings or transcripts yet.
                                    Upload a recording to get started.
                                </p>
                                <button
                                    onClick={() => navigate('/upload')}
                                    style={{
                                        padding: '12px 24px',
                                        background: 'linear-gradient(45deg, #034FAF, #0367d4)',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '8px',
                                        fontWeight: '600',
                                        cursor: 'pointer',
                                        transition: 'all 0.3s ease',
                                        fontSize: '14px',
                                        boxShadow: '0 4px 15px rgba(3, 79, 175, 0.3)',
                                    }}
                                    onMouseOver={(e) => {
                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                        e.currentTarget.style.boxShadow = '0 6px 20px rgba(3, 79, 175, 0.4)';
                                    }}
                                    onMouseOut={(e) => {
                                        e.currentTarget.style.transform = 'translateY(0)';
                                        e.currentTarget.style.boxShadow = '0 4px 15px rgba(3, 79, 175, 0.3)';
                                    }}
                                >
                                    Upload a Recording
                                </button>
                            </div>
                        ) : (
                            // Table with transcripts
                            <table style={{
                                width: '100%',
                                borderCollapse: 'separate',
                                borderSpacing: '0 12px'
                            }}>
                                <thead>
                                    <tr>
                                        <th style={{...tableHeaderStyle, width: '40%', cursor: 'default'}}>TITLE</th>
                                        <th style={{...tableHeaderStyle, width: '25%', cursor: 'default'}}>CREATED AT</th>
                                        <th style={{...tableHeaderStyle, width: '25%', cursor: 'default'}}>UPDATED AT</th>
                                        <th style={{...tableHeaderStyle, width: '10%', textAlign: 'center', cursor: 'default'}}></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {meetingMinutes.map((meeting, index) => (
                                        <tr
                                            key={meeting.id}
                                            onClick={() => handleRowClick(meeting.id)}
                                            style={{
                                                cursor: 'pointer',
                                                transition: 'all 0.3s ease',
                                                background: 'rgba(255, 255, 255, 0.5)',
                                                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.02)',
                                                borderRadius: '8px',
                                                backdropFilter: 'blur(5px)',
                                                WebkitBackdropFilter: 'blur(5px)',
                                                border: '1px solid rgba(255, 255, 255, 0.3)',
                                                animation: `fadeIn 0.5s ease-out ${index * 0.1}s both`,
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.transform = 'translateY(-2px)';
                                                e.currentTarget.style.boxShadow = '0 4px 12px rgba(3, 79, 175, 0.1)';
                                                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.7)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.transform = 'translateY(0)';
                                                e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.02)';
                                                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.5)';
                                            }}
                                        >
                                            <td style={{...tableDataStyle, fontWeight: '600', color: '#034FAF'}}>{meeting.title}</td>
                                            <td style={tableDataStyle}>{meeting.createdAt}</td>
                                            <td style={tableDataStyle}>{meeting.updatedAt}</td>
                                            <td style={{...tableDataStyle, textAlign: 'center'}}>
                                                <button
                                                    onClick={(e) => handleDelete(e, meeting.id, meeting.title)}
                                                    style={{
                                                        background: 'none',
                                                        border: 'none',
                                                        cursor: 'pointer',
                                                        padding: '8px',
                                                        borderRadius: '4px',
                                                        display: 'inline-flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        transition: 'background 0.3s',
                                                    }}
                                                    onMouseOver={(e) => {
                                                        e.currentTarget.style.background = 'rgba(220, 38, 38, 0.1)';
                                                    }}
                                                    onMouseOut={(e) => {
                                                        e.currentTarget.style.background = 'none';
                                                    }}
                                                    title="Delete"
                                                >
                                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M3 6H5H21" stroke="#DC2626" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="#DC2626" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        )}
                    </div>
                </div>
            </div>

            <style>
                {`
                    @keyframes backgroundShift {
                        0% { background-position: 0 0; }
                        100% { background-position: 100% 100%; }
                    }

                    @keyframes float {
                        0% { transform: translateY(0) rotate(0deg); }
                        50% { transform: translateY(-15px) rotate(5deg); }
                        100% { transform: translateY(0) rotate(0deg); }
                    }

                    @keyframes shimmer {
                        0% { opacity: 0.7; }
                        50% { opacity: 0.9; }
                        100% { opacity: 0.7; }
                    }

                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }

                    /* Hide scrollbar for Chrome, Safari and Opera */
                    *::-webkit-scrollbar {
                        display: none;
                    }

                    /* Hide scrollbar for IE, Edge and Firefox */
                    * {
                        -ms-overflow-style: none;  /* IE and Edge */
                        scrollbar-width: none;  /* Firefox */
                    }

                    /* Default cursor for text elements */
                    h1, h2, h3, h4, h5, h6, p, span, div, section, label, th {
                        cursor: default !important;
                    }

                    /* Ensure buttons and interactive elements have pointer cursor */
                    button, a, .clickable, [role="button"], input[type="file"], input[type="submit"] {
                        cursor: pointer !important;
                    }

                    /* Make table rows with pointer cursor */
                    tr[onClick] {
                        cursor: pointer !important;
                    }
                `}
            </style>
        </div>
    );
};

const tableHeaderStyle = {
    textAlign: 'left',
    padding: '15px 20px',
    color: '#566573',
    fontWeight: '600',
    fontSize: '14px',
    letterSpacing: '0.5px',
    borderBottom: '2px solid #f1f1f1'
};

const tableDataStyle = {
    padding: '15px 20px',
    color: '#2c3e50',
    fontSize: '14px',
    fontWeight: '500',
    whiteSpace: 'nowrap',
    borderBottom: '1px solid rgba(241, 241, 241, 0.5)'
};

export default ConversationList;
